version: '3.8'

services:
  # Development service for running both frontend and backend
  dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    ports:
      - "3000:3000" # Frontend port
      - "5000:5000" # Backend port
    environment:
      - NODE_ENV=development
      - PORT=5000
      - FRONTEND_URL=http://localhost:3000
      - VITE_API_BASE_URL=http://localhost:5000/api
    volumes:
      # Mount source code for live reloading
      - ./:/app
      # Prevent node_modules from being overwritten by the host
      - /app/node_modules
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s # Longer start period for development as it may take longer to start
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - chewyai-network

networks:
  chewyai-network:
    driver: bridge
