import React, { useEffect, useState } from "react";
import { useRoute } from "wouter";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/hooks/useAuth";
import { DocumentViewer } from "@/components/document/DocumentViewer";
import { Tables } from "@/types/supabase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, FileText } from "lucide-react";
import { useLocation } from "wouter";
import Spinner from "@/components/ui/Spinner";

type StudyDocument = Tables<"study_documents">;

export const DocumentViewPage: React.FC = () => {
  const [, params] = useRoute("/documents/:documentId");
  const [, navigate] = useLocation();
  const { user } = useAuth();
  const [document, setDocument] = useState<StudyDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDocument = async () => {
      if (!params?.documentId || !user) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const { data, error: dbError } = await supabase
          .from("study_documents")
          .select("*")
          .eq("id", params.documentId)
          .eq("user_id", user.id)
          .single();

        if (dbError) {
          if (dbError.code === "PGRST116") {
            setError(
              "Document not found or you don't have permission to view it."
            );
          } else {
            throw dbError;
          }
          return;
        }

        setDocument(data);
      } catch (err: any) {
        console.error("Error fetching document:", err);
        setError(err.message || "Failed to load document.");
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();
  }, [params?.documentId, user]);

  const handleClose = () => {
    navigate("/");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <Card className="bg-slate-800 border-slate-700 p-8">
          <CardContent className="flex flex-col items-center space-y-4">
            <Spinner size="lg" />
            <p className="text-slate-300">Loading document...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
        <Card className="bg-slate-800 border-slate-700 max-w-md w-full">
          <CardHeader>
            <CardTitle className="text-red-400 flex items-center">
              <FileText className="mr-2" />
              Document Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-slate-300">{error || "Document not found."}</p>
            <Button
              onClick={handleClose}
              variant="outline"
              className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <DocumentViewer document={document} onClose={handleClose} />
    </div>
  );
};
