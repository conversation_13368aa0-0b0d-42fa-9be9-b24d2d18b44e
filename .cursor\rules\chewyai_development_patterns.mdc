---
description: 
globs: *.tsx,*.js
alwaysApply: false
---
# ChewyAI Development Patterns & User Preferences

## User Work Style Preferences
- **Never start servers**: User manages their own development environment and server startup
- **Direct implementation**: Always implement solutions directly without asking for approval
- **No code comments**: Never add comments to code - keep it clean and self-documenting
- **Object-oriented & type-safe**: Follow OOP principles and maintain strict TypeScript typing
- **Keep iterating**: Don't stop until entire PRD/feature is fully implemented
- **Follow user's coding style**: Match established patterns in the codebase

## ChewyAI Architecture Patterns

### Security-First Backend Design
- **Private storage**: Always use private Supabase storage buckets, never public URLs
- **Backend-mediated access**: All file operations go through authenticated backend endpoints
- **JWT authentication**: Use Bearer tokens for all API calls
- **User ownership verification**: Always verify user owns resources before access
- **Ephemeral credentials**: User AI keys handled in-memory only, never persisted

### API Design Standards
```typescript
// Standard authenticated endpoint pattern
async function getAuthenticatedUser(req: Request): Promise<{error: string, status: number} | {user: {id: string}}> {
  const authHeader = req.headers.authorization;
  if (!authHeader?.startsWith("Bearer ")) {
    return { error: "Unauthorized: Missing or malformed token", status: 401 };
  }
  const token = authHeader.split(" ")[1];
  const { data, error } = await supabaseClient.auth.getUser(token);
  if (error || !data?.user) {
    return { error: "Unauthorized: Invalid token", status: 401 };
  }
  return { user: data.user };
}
```

### Frontend API Integration
```typescript
// Standard API client pattern
async function getAuthHeaders(): Promise<HeadersInit> {
  const { data: { session } } = await supabase.auth.getSession();
  return {
    "Content-Type": "application/json",
    ...(session?.access_token && { Authorization: `Bearer ${session.access_token}` }),
  };
}
```

### File Upload Security Pattern
1. **Client-side extraction**: Extract text client-side (pdf.js, mammoth.js)
2. **Backend upload**: Send extracted content to secure backend endpoint
3. **Storage isolation**: Store files in user-specific paths (`${user.id}/extracted_text/`)
4. **Database consistency**: Always cleanup storage if database operations fail
5. **Content serving**: Serve content through authenticated backend endpoints, never direct storage access

### Database Operations via Supabase MCP
- Use Supabase MCP server for all database schema changes
- Apply migrations for DDL operations
- Direct SQL execution for queries only
- Always include proper RLS policies
- Verify project ID before operations

### React Query Integration
```typescript
// Standard query invalidation after mutations
await queryClient.invalidateQueries({
  queryKey: ["study-documents", user.id],
});
```

### Error Handling Standards
```typescript
// Backend error response pattern
return res.status(400).json({
  error: "Primary error message",
  details: specificError.message,
});

// Frontend error handling
try {
  const response = await fetch(url, options);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
} catch (error) {
  console.error("Operation failed:", error);
  throw error;
}
```

## Technology Stack Patterns

### Backend (Node.js/Express.js/TypeScript)
- Express.js with TypeScript for all backend services
- Zod schemas for request validation
- Supabase client for database/storage operations
- Proper async/await error handling
- Environment variables via .env files

### Frontend (React/TypeScript)
- React with TypeScript for all components
- Tailwind CSS for styling (space/galaxy theme)
- React Query for data fetching and caching
- Zustand or React Context for state management
- Client-side file processing (pdf.js, mammoth.js)

### Database & Storage
- Supabase PostgreSQL for all persistent data
- Private storage buckets for all file content
- RLS policies for user data isolation
- UUID primary keys for documents
- Proper foreign key relationships

## File Organization Patterns
```
server/
  routes/
    documentRoutes.ts    # All document CRUD + content serving
  middleware/
    supabaseMiddleware.ts # Shared Supabase client
client/
  src/
    lib/
      api.ts            # All backend API calls
      supabaseClient.ts # Frontend Supabase client
    components/
      document/
        DocumentViewer.tsx # Secure content display
    hooks/
      useDocuments.ts   # React Query integration
```

## Security Checklist for New Features
- [ ] Authentication required for all sensitive endpoints
- [ ] User ownership verification for all resource access
- [ ] Private storage for all user content
- [ ] Backend-mediated content serving
- [ ] Proper error handling without information leakage
- [ ] Input validation with Zod schemas
- [ ] CORS headers properly configured
- [ ] No sensitive data in client-side logs

## Testing & Debug Patterns
- Include debug components for testing new features
- Test both successful and error scenarios
- Verify authentication flows
- Test file upload/download flows
- Validate React Query cache invalidation
- Check browser developer tools for network errors

## Common Anti-Patterns to Avoid
- Never use `getPublicUrl()` for private content
- Never store user credentials persistently
- Never skip authentication checks
- Never expose internal error details to clients
- Never use direct Supabase calls for sensitive operations from frontend
- Never forget to cleanup storage on database operation failures

## Environment Configuration
```typescript
// Backend
const API_BASE_URL = process.env.API_BASE_URL || "http://localhost:5000";

// Frontend  
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:5000/api";
```

## React Component Patterns
- Always include loading states
- Proper error boundaries and error displays
- Toast notifications for user feedback
- Responsive design with Tailwind classes
- Proper TypeScript prop interfaces
- Use React Query for all server state

