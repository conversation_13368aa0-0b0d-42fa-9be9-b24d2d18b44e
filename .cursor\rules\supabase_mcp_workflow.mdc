---
description: Supabase MCP Workflow Documentation
globs: 
alwaysApply: false
---
# Supabase MCP Server Workflow Patterns

## Database Operations Protocol
Always use Supabase MCP server for database operations instead of manual SQL or CLI commands.

### Project Management
```
1. List projects to find project ID
2. Verify project status before operations
3. Use project ID for all subsequent operations
```

### Migration Workflow
```
1. Use `apply_migration` for all DDL operations (CREATE TABLE, ALTER TABLE, etc.)
2. Use descriptive snake_case names for migrations
3. Include proper RLS policies in all table creation migrations
4. Test migrations in development before production
```

### Storage Bucket Management
```
1. Create storage buckets via migrations when possible
2. Always set up proper storage policies
3. Use private buckets for user content
4. Organize files with user-specific paths: `${user_id}/category/filename`
```

### Standard Table Patterns

#### User-Owned Tables
```sql
CREATE TABLE table_name (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  ...other_fields
);

-- RLS Policies
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own records" ON table_name
  FOR ALL USING (auth.uid() = user_id);
```

#### Storage Policies
```sql
-- Bucket policy for user isolation
CREATE POLICY "Users can upload their own files" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'bucket_name' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own files" ON storage.objects
  FOR SELECT USING (bucket_id = 'bucket_name' AND auth.uid()::text = (storage.foldername(name))[1]);
```

## Error Handling
- Always check MCP operation results
- Log meaningful error messages
- Provide fallback options when possible
- Never assume operations succeeded without verification

## Project-Specific Patterns for ChewyAI

### Document Management
- Use `study_documents` table for metadata
- Store content in `study_materials` bucket
- Path pattern: `${user_id}/extracted_text/${document_id}.txt`
- Always verify user ownership before operations

### Migration Naming Convention
- Format: `verb_noun_description`
- Examples: `create_study_documents_table`, `add_quiz_generation_tables`, `update_document_status_column`

### Common Operations
```
1. Create table + RLS policies + storage bucket (if needed)
2. Test table access with sample data
3. Verify storage operations work correctly
4. Update backend routes to use new schema
5. Update frontend types and queries
```

## Debugging MCP Operations
- Use `list_tables` to verify table creation
- Use `execute_sql` for quick data verification
- Check storage bucket contents with `list` operations
- Monitor logs for authentication and permission issues

## Best Practices
- Keep migrations atomic and reversible
- Test RLS policies thoroughly
- Use consistent naming conventions
- Document complex migrations in commit messages
- Backup before major schema changes

