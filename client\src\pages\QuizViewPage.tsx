import React from "react";
import { useParams, useLocation } from "wouter";
import AppLayout from "@/components/layout/AppLayout";
import { QuizPlayer } from "@/components/quiz/QuizPlayer";

const QuizViewPage: React.FC = () => {
  const params = useParams<{ quizId: string }>();
  const [, navigate] = useLocation();
  const quizId = params?.quizId;

  return (
    <AppLayout title="Play Quiz">
      <div className="max-w-2xl mx-auto">
        {quizId ? (
          <QuizPlayer quizId={quizId} onExit={() => navigate("/quizzes")} />
        ) : (
          <p className="text-slate-200 text-lg text-center py-10">
            Invalid Quiz ID.
          </p>
        )}
      </div>
    </AppLayout>
  );
};

export default QuizViewPage;
