@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%; /* White */
  --foreground: 224 71.4% 4.1%; /* Dark Blue-Gray */
  --muted: 220 14.3% 95.9%; /* Light Gray */
  --muted-foreground: 220 8.9% 43.1%; /* Medium Gray */
  --popover: 0 0% 100%;
  --popover-foreground: 224 71.4% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 224 71.4% 4.1%;
  --border: 220 13% 91%; /* Lighter Gray */
  --input: 220 13% 91%;
  --primary: 260 70% 55%; /* Vibrant Purple */
  --primary-foreground: 0 0% 100%; /* White */
  --secondary: 220 14.3% 95.9%; /* Light Gray - used for subtle backgrounds */
  --secondary-foreground: 260 70% 45%; /* Darker Purple */
  --accent: 260 70% 65%; /* Lighter Purple for accents */
  --accent-foreground: 0 0% 100%; /* White */
  --destructive: 0 72.2% 50.6%; /* Red */
  --destructive-foreground: 0 0% 100%;
  --success: 142.1 70.6% 45.3%; /* Green */
  --success-foreground: 0 0% 100%;
  --warning: 38.9 91.7% 50%; /* Yellow */
  --warning-foreground: 224 71.4% 4.1%; /* Dark Blue-Gray for contrast */
  --info: 200 90% 50%; /* Blue */
  --info-foreground: 0 0% 100%;
  --ring: 260 70% 55%;
  --radius: 0.75rem; /* Slightly larger radius for a softer look */
}

.dark {
  --background: 224 71.4% 4.1%; /* Dark Blue-Gray */
  --foreground: 210 20% 98%; /* Off-White */
  --muted: 220 13% 18%; /* Darker Gray */
  --muted-foreground: 220 5% 55%; /* Lighter Gray for muted text */
  --popover: 224 71.4% 4.1%;
  --popover-foreground: 210 20% 98%;
  --card: 224 71.4% 6.1%; /* Slightly lighter than background for cards */
  --card-foreground: 210 20% 98%;
  --border: 220 13% 22%; /* Darker border */
  --input: 220 13% 22%;
  --primary: 260 70% 60%; /* Slightly lighter purple for dark mode */
  --primary-foreground: 0 0% 100%;
  --secondary: 220 13% 18%; /* Darker Gray */
  --secondary-foreground: 210 20% 98%;
  --accent: 260 70% 65%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 62.8% 50.6%; /* Slightly muted red for dark mode */
  --destructive-foreground: 0 0% 100%;
  --success: 142.1 70.6% 45.3%;
  --success-foreground: 0 0% 100%;
  --warning: 38.9 91.7% 50%;
  --warning-foreground: 224 71.4% 4.1%;
  --info: 200 90% 50%;
  --info-foreground: 0 0% 100%;
  --ring: 260 70% 60%;
  --radius: 0.75rem;
}

@layer base {
  * {
    @apply border-border transition-colors duration-200 ease-in-out; /* Add subtle transition to all elements */
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif; /* Using Inter as a modern sans-serif font */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }
  h2 {
    @apply text-3xl lg:text-4xl;
  }
  h3 {
    @apply text-2xl lg:text-3xl;
  }
  h4 {
    @apply text-xl lg:text-2xl;
  }
  h5 {
    @apply text-lg lg:text-xl;
  }
  h6 {
    @apply text-base lg:text-lg;
  }

  p {
    @apply leading-relaxed text-muted-foreground;
  }

  a {
    @apply text-primary hover:text-accent transition-colors duration-150;
  }

  /* Basic button styling - can be extended by specific button components */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
    @apply px-4 py-2; /* Default padding */
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/80;
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  .btn-link {
    @apply text-primary underline-offset-4 hover:underline;
  }

  /* Input field base style */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="number"],
  textarea,
  select {
    @apply block w-full rounded-md border-input bg-background px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Utility classes for legacy components */
  .input-class {
    @apply block w-full rounded-md border-input bg-background px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50;
  }

  .select-class {
    @apply block w-full rounded-md border-input bg-background px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50;
  }

  .checkbox-class {
    @apply h-4 w-4 text-primary border-input rounded focus:ring-ring focus:ring-2;
  }
}

/* Flashcard styles */
.flashcard-container {
  perspective: 1000px;
}

.flashcard-inner {
  transition: transform 0.6s;
  transform-style: preserve-3d;
  position: relative;
  width: 100%;
  height: 100%;
}

.flashcard-flipped .flashcard-inner {
  transform: rotateY(180deg);
}

.flashcard-front,
.flashcard-back {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: absolute;
  width: 100%;
  height: 100%;
}

.flashcard-back {
  transform: rotateY(180deg);
}

/* Material Icons Styling */
.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px; /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  vertical-align: middle;
}

.material-icons.md-18 {
  font-size: 18px;
}
.material-icons.md-24 {
  font-size: 24px;
}
.material-icons.md-36 {
  font-size: 36px;
}
.material-icons.md-48 {
  font-size: 48px;
}

/* Add flashcard animation styles at the end of the file */

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.backface-hidden {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
