---
description: Product Requirements Documentation
globs: 
alwaysApply: false
---
# Product Requirements Document: ChewyAI
**Date:** May 12, 2025

## I. Introduction
The landscape of educational technology is rapidly evolving, with Artificial Intelligence (AI) playing an increasingly pivotal role in personalizing and enhancing the learning experience. ChewyAI is envisioned as a powerful, user-centric study tool that leverages modern web technologies and flexible AI capabilities, designed for local development and user-managed deployment.
This document presents a detailed Product Requirements Document (PRD) for "ChewyAI." ChewyAI will be an open-sourced application featuring a browser-based frontend built with React (using TypeScript) and a Node.js/Express.js (TypeScript) backend service. The frontend will provide the user interface, while the backend will securely manage AI interactions and database operations. This architecture prioritizes secure API key management (with user-provided keys handled ephemerally by the backend, and backend-specific keys managed via .env files), flexible AI model integration (specifically google/gemini-2.5-flash for extraction tasks and google/gemini-2.5-pro for content generation via OpenRouter), a delightful and responsive user experience with a primary dark purply space/galaxy aesthetic (and options for theme customization like light mode), significant client-side processing to optimize backend load, and community-driven development. Future enhancements include considerations for immersive sound effects. Users will be responsible for hosting both the frontend and backend components.

## II. ChewyAI Core Concepts and Architecture
ChewyAI aims to make studying more efficient and effective by automating the creation of study aids and personalizing the learning journey, with a keen eye on resource optimization through its distinct frontend and backend architecture.

### A. Core AI-Driven Learning Capabilities (via Backend Service)
The locally hosted Node.js backend will interface with configured AI models to generate various study materials.
* **AI-Powered Content Generation:** The backend will utilize user-configured AI models, defaulting to OpenRouter with google/gemini-2.5-flash for tasks like text extraction/summarization and google/gemini-2.5-pro for generating detailed study aids.
* **Contextual Understanding:** AI models will be prompted by the backend to understand the context of user-provided materials.
* **Adaptive Learning Support:** Generated materials and future features will aim to support adaptive learning principles.
* **Spaced Repetition System (SRS):** Primarily client-side logic for performance, with SRS data potentially persisted via the backend to a local SQLite database (or optionally Supabase in the future) for V1.0.
* **Personalized Summaries & Explanations:** The backend can request summaries or explanations from AI models based on user content.
* **Intelligent Feedback:** AI can be used by the backend to provide feedback on user responses.
* **Interactive Q&A with Materials:** Users can ask questions about their study materials, facilitated by the backend and AI.

### B. Content Input and Management (Emphasis on Client-Side Processing)
* **Client-Side File Uploads & Text Extraction:** The React frontend will handle file uploads (PDF, TXT, MD, DOCX). Text extraction from these files will occur client-side (e.g., using pdf.js, mammoth.js) to minimize backend processing load.
* **Text Input:** Users can directly paste text into the application.
* **Data Flow:** Extracted text from the frontend will be sent to the ChewyAI backend API. The backend will then relay this content to the selected AI API (e.g., OpenRouter, or other OpenAI-like services) using user-provided credentials.

### C. Question and Study Material Generation (via Backend AI Calls)
* Flashcards
* Multiple Choice Questions (MCQs)
* Free Response Questions
* Case Study Questions (Future Goal)
* Practice Quizzes
* Summaries

### D. User Experience and Engagement (Frontend Focus)
* **Personalized Study Environment:** Customizable settings and content.
* **Theme Customization:** Ability to toggle between a dark (default space/galaxy) and a light theme via a visual toggle (e.g., sun/moon icon).
* **Progress Tracking:** Visualizing learning progress.
* **Gamification Elements:** (Future Goal) To enhance motivation.
* **Immersive Sound Effects (Future Goal, Post V1.0):** Planned for a later release to enhance user engagement. Implementation will include:
    * User-configurable volume control (e.g., a settings slider from 0-100%).
    * A robust system where sound events are mapped to local sound files via a configuration file (e.g., JSON). If a specific sound file path is empty or invalid in the configuration, the system will gracefully skip playing the sound, allowing for incremental addition of audio assets.
* **User-Friendly Interface:** Adherence to modern design principles with a primary space/galaxy theme featuring a dark purply aesthetic, utilizing Tailwind CSS and React Icons, ensuring mobile-friendliness and responsiveness.

### E. Platform Accessibility and Language Support
* **Platform Availability:**
    * Frontend: Any modern web browser supporting React. Deployed as a static site (user-managed deployment).
    * Backend: Runs as a Node.js/TypeScript service (user-managed deployment on a suitable server environment).
* **Development Environment (Local):**
    * Frontend and backend will be developed locally.
    * `package.json` scripts will utilize a tool like `concurrently` or `npm-run-all` to start both the frontend development server (e.g., Vite/CRA) and the backend Node.js server with a single command (e.g., `npm run dev`).
* **Language Versatility:** Language capabilities will be determined by the configured AI model's support.

### F. Monetization and Cost Model
* **ChewyAI Software:** Open-source and free to use.
* **AI API Costs:** Users will be responsible for costs associated with their chosen AI model API usage (e.g., OpenRouter, OpenAI, Google Gemini directly), using their own API keys.
* **Hosting Costs:** Users will be responsible for any costs associated with deploying and hosting the frontend (e.g., static site hosting) and backend (e.g., server/container hosting) themselves.

## III. Product Requirements Document: ChewyAI

### A. Introduction and Goals
* **Product Name:** ChewyAI
* **Vision:** To provide students and lifelong learners with a powerful, secure, and customizable AI-driven study tool, optimized for local development and flexible deployment. ChewyAI will feature a React/TypeScript-based frontend and a Node.js/TypeScript backend, offering flexibility in AI model choice (defaulting to OpenRouter with google/gemini-2.5-flash for extraction and google/gemini-2.5-pro for content generation) and a clean, responsive interface with a captivating space/galaxy theme (with light/dark mode options) and dark purply aesthetic.
* **Mission:** ChewyAI aims to empower users by providing a secure environment for AI interaction (user API keys managed ephemerally by the backend, with backend-specific credentials managed via .env files), control over their study processes, and freedom from proprietary app subscription models. As an open-source project, it will foster a community dedicated to advancing AI educational technology with excellent UI/UX, including considerations for future enhancements like sound effects.
* **Goals for V1.0 (MVP First):**
    * Core Functionality (MVP): Deliver a robust React/TypeScript application (frontend) and a Node.js/Express.js/TypeScript backend API. The backend will use a configurable AI provider (default: OpenRouter with google/gemini-2.5-flash for client-side extracted text processing and google/gemini-2.5-pro for generating flashcards) from user-provided documents.
    * Security & AI Flexibility (MVP):
        * Backend securely handles user-provided AI API credentials (Base URL, API Key, Model ID) ephemerally per request or short-lived secure session. These user keys are not to be stored persistently by the ChewyAI backend.
        * Any backend-specific API keys or configurations (e.g., a default ChewyAI-owned OpenRouter key for a limited demo mode, if ever implemented) will be managed using `.env` files.
        * Frontend allows users to configure their preferred OpenAI-like AI provider details, which are then used by the backend for AI calls.
    * User Data Management (MVP): User-generated content (flashcards, notes, SRS data) to be stored primarily in the user's browser (localStorage/IndexedDB) with robust export options (JSON, CSV). This prioritizes user control and minimizes backend storage needs for MVP.
    * User Experience (MVP): Provide an intuitive web application for managing study materials and engaging with generated content, adhering to a default space/galaxy theme with a dark purply aesthetic, styled with Tailwind CSS, using React Icons, and ensuring a seamless experience on desktop and mobile. (Theme toggling planned for V1.0 enhancement).
    * Development & Deployment (MVP): Frontend and backend developed locally, with a single command (e.g., `npm run dev` using `concurrently`) to run both for development. Users are responsible for deploying the frontend (e.g., as a static site) and the backend (e.g., to a Node.js compatible server environment).
    * Open Source (MVP): Establish a well-documented, accessible codebase (React/TypeScript frontend, Node.js/TypeScript backend) on a platform like GitHub to encourage community contributions.
* **V1.0 Enhancement - Database Persistence:** For users wanting cross-session persistence on their self-hosted instance, introduce optional backend persistence of generated content and SRS data to a local SQLite database. The system should be designed with consideration for future migration or integration with cloud-based databases like Supabase.
* **V1.0 Enhancement - User Experience:** Introduce theme toggling (dark/light mode with sun/moon icon).

### B. Target Audience
* Students (High School, College, University)
* Lifelong Learners & Professionals
* Privacy-Conscious Individuals (who understand their data is sent to their configured AI provider via their self-hosted ChewyAI backend, and that client-side storage is the default for generated content)
* Users who want flexibility in choosing AI models and managing their own API key costs and hosting.
* Open-Source Enthusiasts & Developers (skilled in React, TypeScript, Node.js, Tailwind CSS, AI API integration, and self-hosting).

### C. User Stories
* **MVP (Core Functionality Focus):**
    * US000: As a developer, I want to set up the ChewyAI project locally, and with a single command (e.g., `npm run dev` utilizing `concurrently`), run both the React frontend development server and the Node.js backend server simultaneously for an efficient development workflow.
    * US001: As a student, I want to import a PDF document into the ChewyAI browser application, have its text extracted client-side, and sent to my self-hosted ChewyAI backend for processing by an AI.
    * US002: As a user, I want to configure the ChewyAI application with my AI provider details (Base URL, API Key, Model IDs) which will be securely sent to and used ephemerally by my self-hosted backend for each AI request. The application should default to OpenRouter using google/gemini-2.5-flash for extraction and google/gemini-2.5-pro for content generation.
    * US003: As a learner, I want my self-hosted ChewyAI backend to use my configured AI provider (specifically google/gemini-2.5-pro) to generate a set of flashcards (question/answer pairs) from my document's content and send them back to the frontend for display.
    * US004: As a user, I want to review the generated flashcards within the React frontend, with the ability to flip cards and mark them as known/unknown, all within an engaging space-themed interface.
    * US005: As a student, I want ChewyAI to implement a basic client-side spaced repetition system (SRS) using browser storage (localStorage/IndexedDB) to schedule flashcard reviews.
    * US006: As a privacy-conscious user, I want to be explicitly informed that my document content will be sent to my self-hosted ChewyAI backend, and then to my configured AI provider for processing. I also want to know that my generated study data is stored in my browser by default.
    * US008: As a user, I want a simple interface to manage my imported documents (for the current session/batch) and their associated flashcard decks, with data stored client-side.
    * US008.1: As a user, I want to be able to export my flashcards and study data from browser storage (e.g., as JSON or CSV).
* **V1.0 (Post-MVP Enhancements):**
    * US009: As a learner, I want my self-hosted ChewyAI backend to request multiple-choice quizzes from my configured AI provider (using google/gemini-2.5-pro) based on my imported documents.
    * US010: As a student, I want to take the AI-generated quizzes within the React frontend and receive immediate feedback.
    * US011: As a user, I want to import text files (.txt, .md) and DOCX files (via client-side parsing) for processing.
    * US012: As a learner, I want the client-side SRS to offer more customization for review intervals.
    * US013: As a user, I want to be able to edit the AI-generated flashcards and quiz questions/answers within the React app, with changes saved to browser storage (or optionally to a local SQLite database via the backend if configured).
    * US014: As a student, I want a progress tracking feature to see my learning history and performance, with data primarily from client-side storage, or optionally from a local SQLite database.
    * US015: As a user, I want the React application to provide clear feedback on the status of communication with my self-hosted ChewyAI backend and the AI provider (e.g., loading indicators, error messages).
    * US016: As a user running my own instance, I want an option to have my generated study materials (flashcards, quizzes, SRS data) persisted more permanently using a local SQLite database via the backend. I also want the system designed to potentially support cloud databases like Supabase in the future for enhanced persistence options.
    * US017: As a user, I want to be able to switch between the default dark (space/galaxy) theme and an alternative light theme using a simple toggle (e.g., a sun/moon icon) in the application settings or header.
* **Future Goals (Post V1.0):**
    * US018 (Future Goal): As a user, I want subtle sound effects for key interactions (e.g., card flips, quiz completion, notifications) to enhance engagement, with the ability to control the sound effect volume from 0-100% via a slider in the settings, or mute them entirely.

### D. Product Features (Minimum Viable Product - MVP)
The MVP will focus on: client-side PDF import & text extraction, a self-hosted Node.js/TypeScript backend for AI interaction using configurable OpenAI-like models (default OpenRouter: google/gemini-2.5-flash for extraction, google/gemini-2.5-pro for generation), AI-powered flashcard generation, and client-side review with SRS and data export.
* **React Frontend Application (TypeScript, Static Deployment, User-Managed):**
    * Client-Side Document Import & Parsing: PDF file import (using File API), client-side text extraction (e.g., pdf.js).
    * AI Provider Configuration UI: Interface for users to input Base URL, API Key, and Model IDs. Defaults to OpenRouter (google/gemini-2.5-flash, google/gemini-2.5-pro). Configuration stored in browser's localStorage.
    * Flashcard Review Interface: Display flashcards, reveal answer, mark difficulty.
    * Client-Side Spaced Repetition System (SRS): Basic SRS algorithm, data in localStorage/IndexedDB.
    * Data Export: Functionality to export generated content (flashcards, SRS data) as JSON or CSV.
    * UI/UX: Default space/galaxy theme with a dark purply aesthetic, styled with Tailwind CSS, React Icons, responsive.
    * Communication: API calls to the self-hosted ChewyAI backend for AI tasks. Clear status indicators. Backend API URL configurable (e.g., via build-time environment variable or runtime configuration).
* **Node.js Backend Service (TypeScript, Express.js, User-Deployed):**
    * API Endpoints: For receiving text content from frontend and returning AI-generated materials (e.g., `/api/generate-flashcards`).
    * AI Model Interaction Module:
        * Connects to the user-configured AI provider (using provided Base URL, API Key, Model ID sent with each request or from a secure, temporary session).
        * Sends processed text to the AI for specific tasks (e.g., google/gemini-2.5-flash for initial text processing/summarization if needed, google/gemini-2.5-pro for flashcard generation).
        * Handles API responses and errors from the AI provider.
    * Secure API Key Management:
        * User-provided AI provider credentials are handled ephemerally by the backend for the duration of the AI transaction. They are NOT stored persistently or logged.
        * Backend-owned API keys or sensitive configurations (if any) managed via `.env` files.
* **Data Storage (MVP):**
    * User AI Configuration: Stored in browser's localStorage on the client-side.
    * Generated Flashcards & SRS Data: Stored in browser localStorage or IndexedDB.
* **User Consent:** Clear information regarding data flow (client -> self-hosted backend -> AI provider) and browser storage.

### E. Product Features (Version 1.0 - Post-MVP Enhancements)
* **Expanded AI Content Generation (via Backend):**
    * Request AI-powered Multiple Choice Question (MCQ) generation (using google/gemini-2.5-pro).
* **Quiz Module (Frontend):**
    * Interface for taking MCQs, immediate feedback, score summary.
* **Additional Client-Side Import Formats:**
    * .txt, .md, DOCX (e.g., using mammoth.js client-side).
* **Enhanced SRS (Client-Side or Backend-Assisted):**
    * User-configurable SRS intervals.
* **Content Editing (Frontend):**
    * Manually edit AI-generated flashcards and MCQs. Changes saved to browser storage or optionally to the configured database via backend.
* **Backend Data Persistence (Optional, User-Instance with SQLite/Supabase):**
    * Option to save and retrieve generated study sets (flashcards, quizzes, SRS data) using a local SQLite database via backend APIs.
    * Architectural consideration for future integration with cloud databases like Supabase.
    * Clear schema design for SQLite.
* **Progress Tracking & Basic Analytics (Frontend with Optional Backend Data):**
    * Dashboard showing study history, potentially using data from the configured database if persistence is enabled.
* **Improved UI/UX:**
    * Refined space/galaxy theme, enhanced feedback during API calls, client-side search within decks.
    * Theme Toggling: Implementation of a light theme alternative to the default dark space/galaxy theme, with an easy-to-use toggle (e.g., sun/moon icon).
    * Settings panel (for AI config, theme adjustments, data management options including database setup if applicable).
* **Sound System Framework (Post V1.0 / Future Goal - Infrastructure):**
    * Initial framework for sound effects.
    * Sound Configuration: System to manage sound events and corresponding local file paths via a JSON configuration file. The design will ensure that if a sound path is empty or invalid, no error occurs, and no sound is played, allowing for future easy addition of sound assets.
    * Volume Control: A UI slider in settings to adjust sound effect volume from 0-100%. Actual sound files and widespread event triggering will be implemented in later iterations.

### F. Non-Functional Requirements
* **Performance:**
    * Client-Side Dominance: Prioritize client-side operations (text extraction, UI rendering, SRS logic for MVP) to ensure fast perceived performance and reduce backend load.
    * AI API Interaction: Backend manages calls efficiently; frontend handles asynchronous updates gracefully.
    * Application Responsiveness: Fast load times for frontend, responsive UI on desktop/mobile. Backend API response times should be reasonable, acknowledging AI provider latency.
* **Usability:**
    * Intuitive interface, easy for non-technical users. Clear guidance on AI provider configuration.
    * Customizable experience through theme toggling (V1.0) and future sound controls.
    * Transparent communication about data flow (frontend -> self-hosted backend -> AI) and storage (client-side default, optional SQLite/Supabase).
* **Security & Privacy:**
    * User Data Handling: Text extraction client-side. Content sent to self-hosted ChewyAI backend, then to user's configured AI provider.
    * AI API Key Security: User's AI provider credentials (Base URL, API Key, Model ID) are handled ephemerally by the backend per transaction. The backend must not log these keys and should use them only in-memory for the API call to the AI provider. Backend-specific, non-user credentials managed via `.env` files.
    * No Data Transmission by ChewyAI to other servers (besides the user-configured AI provider via the self-hosted backend, and the configured database (SQLite/Supabase) if explicitly set up by the user on their instance).
* **Reliability & Stability:**
    * Stable frontend and backend. Graceful handling of API errors (from backend or AI provider).
* **Maintainability (Open Source Context):**
    * Well-structured React/TypeScript (frontend) and Node.js/TypeScript (backend) codebases. Clear contribution guidelines. Code easily runnable locally with concurrent server startup.
* **Scalability:**
    * Backend scalability will depend on the user's chosen hosting environment and configuration for their self-hosted instance. The heavy reliance on client-side processing will aid scalability.
    * Frontend (static site) is inherently scalable depending on the chosen hosting solution.
    * Sound system designed for future scalability by allowing incremental addition of sound files.
* **Accessibility (Future Goal):** Strive for WCAG AA.
* **Platform Support:**
    * Frontend: Modern desktop/mobile web browsers.
    * Backend: Node.js compatible server environment (user-managed).

### G. Technical Considerations
* **Frontend-Backend Communication:**
    * RESTful API calls from React/TypeScript frontend to Node.js/Express.js/TypeScript backend.
    * Managing state in React (e.g., Zustand or React Context) for asynchronous data fetching and UI updates.
    * The frontend will need to be configured with the backend's URL (e.g., through a build-time environment variable or a runtime configuration file).
* **Backend Architecture (Node.js/TypeScript/Express.js, Self-Hosted):**
    * Local Development Setup: Developers will use a tool like `concurrently` or `npm-run-all` (configured in `package.json` via a script like `npm run dev`) to start both the frontend development server and the backend Node.js server with a single command.
    * Organize backend into routes, controllers, services for clarity.
* **AI Model Integration Strategy (Backend):**
    * Backend service to construct and send requests to the configured AI provider's Base URL using the user-provided API Key and Model ID.
    * Specific model usage:
        * `google/gemini-2.5-flash` (via OpenRouter) for tasks like initial text processing, summarization of chunks, or other less complex AI tasks.
        * `google/gemini-2.5-pro` (via OpenRouter) for primary content generation like flashcards, MCQs, and detailed explanations.
    * Support for OpenAI-like API structure (e.g., `POST /v1/chat/completions`).
    * Prompt Engineering: Critical for quality results. Prompts should be well-defined and tested for both `gemini-2.5-flash` and `gemini-2.5-pro` contexts. Initial prompts will be basic, with iteration expected.
    * Data Chunking: Backend must implement logic to handle large text inputs by chunking them appropriately for the target AI API's context window limits, potentially with overlap if necessary for coherence.
    * Error Handling: Robust handling of AI API errors (rate limits, content filters, timeouts) with clear feedback to the frontend.
* **Local Environment and Database Integration (Backend):**
    * `.env` Files: For storing API keys (backend-specific, if any), database connection strings (for SQLite path or Supabase credentials), and other sensitive configurations.
    * Database (SQLite for V1.0, option for Supabase later):
        * SQLite: Default for V1.0 for local, file-based persistence. Requires schema design. Backend APIs for CRUD operations.
        * Supabase (Future Option): Design considerations for allowing users to optionally configure Supabase for cloud-based persistence. This would involve different connection details and potentially minor adjustments to data access logic.
* **Data Management and Privacy:**
    * Client-Side (Default): User AI config in localStorage. Generated content in localStorage/IndexedDB for MVP.
    * Backend: Does not store user document content long-term unless database persistence is configured by the user. User AI keys are handled ephemerally per request.
    * Data Export: Frontend must allow export of user-generated study sets (JSON, CSV) from client-side storage.
* **Frontend UI/UX:**
    * Theme Management: Implement logic for toggling between dark (default) and light themes. Store user preference in localStorage. Use CSS variables or Tailwind's dark mode variant for styling. A sun/moon icon toggle should be used.
    * Sound Effect System (Future Goal - Post V1.0):
        * Configuration: A client-side JSON file (e.g., `public/sounds/sound-manifest.json`) will map abstract sound event names (e.g., `'cardFlip'`, `'quizCorrect'`, `'notification'`) to local sound file paths (e.g., `'./assets/sounds/flip.mp3'`).
        * Playback Logic: A utility function or service will handle sound playback. Before attempting to play a sound, it will check the manifest. If a path for a given event is empty, null, or invalid, the function will silently do nothing, ensuring no errors and allowing sounds to be added incrementally.
        * Volume Control: A global sound volume state (0.0 to 1.0) managed, possibly via React Context or Zustand, and controlled by a slider in the application settings. This volume will be applied to all sound effects played.
        * Technology: Native HTML5 `<audio>` elements or a lightweight library like Howler.js could be considered for managing sound playback.
* **Proposed Technology Stack:**
    * Frontend:
        * Core: TypeScript, React.
        * State Management: Zustand (recommended) or React Context.
        * Styling: Tailwind CSS, React Icons. (UI: Space/galaxy theme with dark/light mode, dark purply aesthetic).
        * Client-side parsing: pdf.js (for PDFs), mammoth.js (for DOCX).
        * Build: Vite (recommended) or Create React App.
    * Backend:
        * Core: Node.js, Express.js, TypeScript.
        * Package Manager: npm or yarn.
        * Development Environment: Local machine.
        * Tooling: `concurrently` or `npm-run-all` for running FE/BE together.
    * AI Engine: User-configurable OpenAI-like API.
        * Default: OpenRouter (API Base: `https://openrouter.ai/api/v1`)
            * Model for extraction/simple tasks: `google/gemini-2.5-flash`
            * Model for content generation: `google/gemini-2.5-pro`
    * Database (V1.0+ for optional persistence):
        * Primary for V1.0: SQLite (local file).
        * Future consideration: Supabase (PostgreSQL).
    * Secrets Management: `.env` files.

### H. Out-of-Scope for Version 1.0
* Actual implementation of a wide range of sound effects (though the framework and volume control may be introduced). The focus is on building the system to support them.
* Cloud synchronization or backup of study data beyond optional SQLite/Supabase (if implemented by the user on their instance) and user-initiated local export.
* Native mobile applications (iOS, Android) – focus is on PWA potential.
* Real-time multi-user collaboration on the same study set.
* Direct import from web URLs or YouTube videos by the backend (client-side fetching could be a future feature if robustly implemented).
* User account management system built by ChewyAI (users manage their own self-hosted instances; no central ChewyAI user accounts).

## IV. Strategic Recommendations and Conclusion
The development of "ChewyAI" with a React/TypeScript frontend and a self-hosted Node.js/TypeScript backend offers a secure, flexible, cost-effective (in terms of software, user bears AI/hosting costs), and modern approach to AI-powered studying.
* **Key Strategic Recommendations:**
    * Prioritize a Lean MVP with Strong Client-Side Focus: Concentrate on reliable client-side text extraction, robust backend interaction with a configurable AI provider (defaulting to OpenRouter with google/gemini-2.5-flash and google/gemini-2.5-pro), secure ephemeral handling of user-provided API keys by the backend, flashcard generation, and a functional client-side SRS with data export. This minimizes backend resource demands.
    * Emphasize Security in Backend Design: Ensure user-provided AI API keys are handled ephemerally and securely by the backend (not logged, used in-memory only for the transaction). Leverage `.env` files correctly for backend-owned sensitive data.
    * Clearly Document AI Configuration & Data Flow: Provide explicit instructions for users on obtaining and inputting their AI provider's Base URL, API Key, and Model IDs. Be transparent about the default OpenRouter models, data flow (client -> self-hosted backend -> AI provider), and data storage (client-side default, optional SQLite/Supabase).
    * Optimize for Local Development and Flexible Deployment: Design for easy local setup with concurrent server startup. Provide guidance or examples for deploying the frontend (static site) and backend (Node.js application) to common hosting platforms.
    * Plan for Richer User Experience: Incorporate theme customization (dark/light mode) in V1.0 and lay the groundwork for a scalable sound effects system for future enhancements, including volume controls.
    * Manage User Expectations: Be transparent about AI capabilities, the necessity for user-provided AI credentials, potential AI API costs (borne by the user), and self-hosting responsibilities and potential costs.
    * Foster Community and Iteration: Encourage open-source contributions. Iterate based on user feedback, especially regarding UI/UX across devices (particularly the space/galaxy theme and theme options), AI model integration, prompt effectiveness for specified Gemini models, database options, and future sound integrations.
* **Conclusion:**



ChewyAI, with its architecture emphasizing client-side processing, a distinct backend for secure AI operations, and designed for local development and self-hosting, is well-positioned to offer a unique and valuable study tool. By clearly separating frontend concerns from backend AI orchestration and secure credential/database management, it enhances security, flexibility, and user control. The focus on user-configurable AI providers (with performant defaults like google/gemini-2.5-flash and google/gemini-2.5-pro), client-side data storage by default, a polished responsive UI/UX featuring a compelling space/galaxy theme with dark/light mode options, and open-source principles will appeal to a wide range of learners. The planned scalability for features like sound effects ensures the application can grow in richness over time. The primary technical challenges will involve seamless frontend-backend integration, robust and secure AI provider communication via the backend, effective prompt engineering for the specified models, and thoughtful, optional integration of SQLite and potentially Supabase for persistence. This PRD provides a clearer roadmap for creating a tool that is functional, secure, flexible, customizable, and empowering for users who wish to run their own instances.