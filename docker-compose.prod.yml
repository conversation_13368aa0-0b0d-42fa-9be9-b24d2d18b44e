version: '3.8'

services:
  # Production service with unified container serving both frontend and backend
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "80:80" # Expose on port 80 for production
    environment:
      - NODE_ENV=production
      - PORT=80
      # Add other production environment variables here
      # - SUPABASE_URL=your_supabase_url
      # - SUPABASE_KEY=your_supabase_key
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:80/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - chewyai-network

networks:
  chewyai-network:
    driver: bridge
