import React from "react";
import { useParams } from "wouter";
import AppLayout from "@/components/layout/AppLayout";
import FlashcardReviewSection from "@/components/flashcards/FlashcardReviewSection";
import { useQuery } from "@tanstack/react-query";
import { getDeck } from "@/lib/storage";

const FlashcardReview: React.FC = () => {
  const params = useParams<{ deckId: string }>();
  const deckId = params?.deckId;
  
  const { data: deck } = useQuery({
    queryKey: [`/api/flashcard-decks/${deckId}`],
    queryFn: () => getDeck(deckId || ""),
    enabled: !!deckId
  });
  
  return (
    <AppLayout title={deck ? `Reviewing: ${deck.name}` : "Flashcard Review"}>
      <FlashcardReviewSection />
    </AppLayout>
  );
};

export default FlashcardReview;
