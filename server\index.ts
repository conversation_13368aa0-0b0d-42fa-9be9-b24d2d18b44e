// Load environment variables first
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Get current file's directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Point specifically to the server/.env file
dotenv.config({ path: path.resolve(__dirname, ".env") });

import express, { type Request, Response, NextFunction } from "express";
import cors from "cors";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
// fileURLToPath import moved to top of file
import { Readable } from "stream"; // Import Readable

// Extend RequestInit to include Node.js specific duplex option
interface NodeRequestInit extends RequestInit {
  duplex?: "half" | "full";
}
import aiRoutes from "./routes/aiRoutes";
import quizRoutes from "./routes/quizRoutes";
import documentRoutes from "./routes/documentRoutes";
import flashcardDeckRoutes from "./routes/flashcardDeckRoutes";
import flashcardRoutes from "./routes/flashcardRoutes";
import flashcardSetRoutes from "./routes/flashcardSetRoutes";
import testRoutes from "./routes/testRoutes";
import healthRoutes from "./routes/healthRoutes";

const app = express();

// CORS configuration for frontend-backend separation
app.use(
  cors({
    // In production with Docker, the frontend and backend are served from the same origin
    // In development, we need to allow cross-origin requests from the dev server
    origin: process.env.NODE_ENV === "production"
      ? true // Allow all origins in production since we're handling CORS at the container level
      : process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false }));

// Debug middleware to log API requests
app.use((req, res, next) => {
  if (req.path.startsWith("/api")) {
    console.log(
      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(
        ", "
      )}`
    );
  }
  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

console.log("🔧 Mounting AI routes at /api");
app.use("/api", aiRoutes);
console.log("🔧 Mounting flashcard deck routes at /api/decks");
app.use("/api/decks", flashcardDeckRoutes);
console.log("🔧 Mounting flashcard routes at /api");
app.use("/api", flashcardRoutes);
console.log("🔧 Mounting test routes at /api");
app.use("/api", testRoutes);
console.log("🔧 Mounting health check routes at /api/health");
app.use("/api/health", healthRoutes);

// Helper function to convert Node.js stream to Web stream if needed
async function nodeStreamToWebStream(
  nodeStream: NodeJS.ReadableStream
): Promise<ReadableStream<Uint8Array>> {
  return new ReadableStream({
    start(controller) {
      nodeStream.on("data", (chunk) =>
        controller.enqueue(new Uint8Array(chunk))
      );
      nodeStream.on("end", () => controller.close());
      nodeStream.on("error", (err) => controller.error(err));
    },
  });
}

// Adapter middleware for Hono to Express
const honoAdapter = (honoApp: any, basePath: string = "") => {
  return async (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    try {
      const hasBody = req.method !== "GET" && req.method !== "HEAD" && req.body;
      const requestBody = hasBody
        ? req.body instanceof Readable
          ? await nodeStreamToWebStream(req.body)
          : new ReadableStream({
              start: (controller) => {
                controller.enqueue(Buffer.from(JSON.stringify(req.body)));
                controller.close();
              },
            })
        : undefined;

      // Strip the base path from the URL for Hono
      let honoPath = req.url;
      if (basePath && req.url.startsWith(basePath)) {
        honoPath = req.url.substring(basePath.length) || "/";
      }

      const webRequest = new Request(
        `${req.protocol}://${req.get("host")}${honoPath}`,
        {
          method: req.method,
          headers: new Headers(req.headers as HeadersInit),
          body: requestBody,
          // Required for Node.js 18+ when sending a body
          duplex: hasBody ? "half" : undefined,
        } as NodeRequestInit
      );

      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);
      const webResponse = await honoApp.fetch(webRequest);

      res.status(webResponse.status);
      webResponse.headers.forEach((value: string, key: string) => {
        res.setHeader(key, value);
      });

      if (webResponse.body) {
        const reader = webResponse.body.getReader();
        const forwardStream = async () => {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            res.write(value);
          }
          res.end();
        };
        await forwardStream();
      } else {
        res.end();
      }
    } catch (error) {
      next(error);
    }
  };
};

// Mount Express routes directly
console.log("🔧 Mounting document routes at /api/documents");
app.use("/api/documents", documentRoutes); // Mount document routes as Express routes

// Mount Hono routes with the adapter
console.log("🔧 Mounting quiz routes at /api/quizzes");
app.use("/api/quizzes", honoAdapter(quizRoutes, "/api/quizzes")); // Mount quiz routes using Hono to Express adapter

console.log("🔧 Mounting flashcard set routes at /api/flashcard-sets");
app.use("/api/flashcard-sets", honoAdapter(flashcardSetRoutes, "/api/flashcard-sets")); // Mount flashcard set routes using Hono to Express adapter

(async () => {
  console.log("🚀 Starting server initialization...");

  try {
    // Set up Vite middleware for development
    if (process.env.NODE_ENV === "development") {
      console.log("🔧 Setting up Vite development middleware...");
      await setupVite(app);
    }

    const server = await registerRoutes(app);
    console.log("✅ Routes registered successfully");

    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
      console.error("Unhandled error in Main Express App:", err);
      const status = err.status || err.statusCode || 500;
      const responseBody: { error: string; message: string; stack?: string } = {
        error: "An unexpected server error occurred.",
        message: err.message || "Internal Server Error",
      };
      // Optionally include stack in development
      if (app.get("env") === "development" && err.stack) {
        responseBody.stack = err.stack;
      }
      res.status(status).json(responseBody);
      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.
    });

    // In development, don't serve static files since Vite handles the frontend
    // In production, serve the built frontend files
    if (app.get("env") !== "development") {
      serveStatic(app);
    }

    // Get port from environment variable or use default
    const port = parseInt(process.env.PORT || "5000", 10);
    const isWindows = process.platform === "win32";
    
    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);

    const httpServer = server.listen(
      {
        port,
        host: "0.0.0.0",
        ...(isWindows ? {} : { reusePort: true }),
      },
      () => {
        log(`✅ Backend server running on port ${port}`);
        log(`🌐 API available at: http://localhost:${port}/api`);
        log(`🔍 Health check: http://localhost:${port}/api/health`);
        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);
      }
    );
    
    // Handle graceful shutdown
    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
    signals.forEach((signal) => {
      process.on(signal, () => {
        log(`Received ${signal}, gracefully shutting down...`);
        
        // Set a timeout for forceful shutdown if graceful shutdown takes too long
        const forcefulShutdownTimeout = setTimeout(() => {
          log('Forceful shutdown timeout reached, exiting immediately!');
          process.exit(1);
        }, 30000); // 30 seconds timeout
        
        // Attempt graceful shutdown
        httpServer.close(() => {
          log('HTTP server closed successfully.');
          clearTimeout(forcefulShutdownTimeout);
          process.exit(0);
        });
      });
    });
  } catch (error: any) {
    console.error("❌ Failed to start server:", error);
    console.error("Error details:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
})();
