# Use Node.js 20 as the base image
FROM node:20-alpine AS base

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (using cache layer)
FROM base AS deps
RUN npm ci

# Development stage
FROM deps AS development
ENV NODE_ENV=development

# Copy source code for development
COPY . .

# Expose ports for development (3000 for frontend, 5000 for backend)
EXPOSE 3000 5000

# Start development servers
CMD ["npm", "run", "dev"]

# Build stage
FROM deps AS builder
ENV NODE_ENV=production

# Copy source code for building
COPY . .

# Build the frontend and backend
RUN npm run build:client && npm run build:server

# Production stage
FROM base AS production
ENV NODE_ENV=production

# Install only production dependencies
RUN npm ci --omit=dev

# Copy built files from builder stage
COPY --from=builder /app/dist /app/dist

# Copy server code needed for production
COPY --from=builder /app/server /app/server
COPY --from=builder /app/shared /app/shared

# Copy start script
COPY docker-start.sh /app/docker-start.sh
RUN chmod +x /app/docker-start.sh

# Expose port 80 for production
EXPOSE 80

# Set environment variables
ENV PORT=80

# Start the server using our start script
CMD ["/app/docker-start.sh"]
