-- Migration to create the quizzes table

CREATE TABLE
  public.quizzes (
    id UUID NOT NULL DEFAULT gen_random_uuid (), -- Use gen_random_uuid() for new UUIDs
    user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
    document_id UUID NULL, -- Can be linked to a study_document or be standalone
    title TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT quizzes_pkey PRIMARY KEY (id)
  );

-- Enable RLS
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;

-- Policies for quizzes table
-- Users can select their own quizzes
CREATE POLICY "Users can select their own quizzes" ON public.quizzes FOR
SELECT
  USING (auth.uid () = user_id);

-- Users can insert their own quizzes
CREATE POLICY "Users can insert their own quizzes" ON public.quizzes FOR INSERT
WITH
  CHECK (auth.uid () = user_id);

-- Users can update their own quizzes
CREATE POLICY "Users can update their own quizzes" ON public.quizzes FOR UPDATE
USING (auth.uid () = user_id)
WITH
  CHECK (auth.uid () = user_id);

-- Users can delete their own quizzes
CREATE POLICY "Users can delete their own quizzes" ON public.quizzes FOR DELETE USING (auth.uid () = user_id);

-- Add indexes
CREATE INDEX idx_quizzes_user_id ON public.quizzes (user_id);
CREATE INDEX idx_quizzes_document_id ON public.quizzes (document_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_quiz_updated_at ()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at on row update
CREATE TRIGGER on_quiz_updated BEFORE UPDATE ON public.quizzes
FOR EACH ROW
EXECUTE PROCEDURE public.handle_quiz_updated_at (); 