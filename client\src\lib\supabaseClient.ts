import { createClient } from "@supabase/supabase-js";
import { Database } from "../types/supabase"; // Assuming you will generate this type

// Use environment variables if available, otherwise fallback to configured values
const supabaseUrl =
  import.meta.env.VITE_SUPABASE_URL ||
  "https://hrdjfukhzbzksqaupqie.supabase.co";
const supabaseAnonKey =
  import.meta.env.VITE_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNDI5NDksImV4cCI6MjA2MjgxODk0OX0.BKJfxPCJFHI-i_m7VE-JqVoUDVNXx2tE0qSDI4JJT2g";

if (!supabaseUrl) {
  throw new Error("VITE_SUPABASE_URL is not set and no fallback available");
}

if (!supabaseAnonKey) {
  throw new Error(
    "VITE_SUPABASE_ANON_KEY is not set and no fallback available"
  );
}

console.log("✓ Supabase client initialized");
console.log("✓ URL:", supabaseUrl);
console.log("✓ Using anon key:", supabaseAnonKey ? "Configured" : "Missing");

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
