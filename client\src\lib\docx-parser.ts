import * as mammoth from 'mammoth';
import { Document } from '@/types';

/**
 * Extract text from a DOCX file
 */
export async function extractTextFromDocx(file: File): Promise<Document> {
  try {
    // Convert file to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Extract text from DOCX
    const result = await mammoth.extractRawText({ arrayBuffer });
    const text = result.value;
    
    return {
      id: crypto.randomUUID(),
      name: file.name,
      content: text.trim(),
      type: 'docx',
      createdAt: Date.now(),
      size: file.size
    };
  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    throw new Error('Failed to extract text from DOCX. Please try another file.');
  }
}

/**
 * Extract text from TXT file
 */
export async function extractTextFromTxt(file: File): Promise<Document> {
  try {
    const text = await file.text();
    
    return {
      id: crypto.randomUUID(),
      name: file.name,
      content: text.trim(),
      type: 'txt',
      createdAt: Date.now(),
      size: file.size
    };
  } catch (error) {
    console.error('Error extracting text from TXT:', error);
    throw new Error('Failed to extract text from text file. Please try another file.');
  }
}

/**
 * Extract text from MD file
 */
export async function extractTextFromMd(file: File): Promise<Document> {
  try {
    const text = await file.text();
    
    return {
      id: crypto.randomUUID(),
      name: file.name,
      content: text.trim(),
      type: 'md',
      createdAt: Date.now(),
      size: file.size
    };
  } catch (error) {
    console.error('Error extracting text from MD:', error);
    throw new Error('Failed to extract text from markdown file. Please try another file.');
  }
}
