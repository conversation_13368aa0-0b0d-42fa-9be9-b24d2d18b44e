import express, { Request, Response } from "express";
import { db } from "../db/drizzle"; // Assuming db instance is exported from '../db/drizzle'
import { quizzes, quizQuestions } from "../db/schema"; // Assuming Drizzle schema is in '../db/schema'
import {
  GenerateQuizParams,
  GenerateQuizResponse,
  Quiz,
  QuizQuestion as SharedQuizQuestion,
  AIProviderConfig,
} from "../../shared/types/quiz";
import { v4 as uuidv4 } from "uuid";
import axios from "axios";

// You'll need to integrate your actual authentication middleware
// import { authenticateToken } from '../middleware/authMiddleware';

const router = express.Router();

interface AIPromptPayload {
  model: string;
  messages: {
    role: "user" | "assistant" | "system";
    content: string;
  }[];
  temperature?: number;
  max_tokens?: number;
  response_format?: { type: "json_object" }; // For models that support JSON mode
}

interface AIQuestionFormat {
  questionText: string;
  options: { text: string }[];
  correctAnswerIndex: number;
  explanation?: string;
}
interface AIResponseFormat {
  questions: AIQuestionFormat[];
}

// Helper function to call AI (e.g., OpenRouter)
async function generateQuestionsFromAI(
  textContent: string,
  numberOfQuestions: number,
  aiConfig: AIProviderConfig,
  quizTitle: string
): Promise<AIResponseFormat | null> {
  const { apiKey, baseUrl, model } = aiConfig;

  const prompt = `Based on the following text, generate a quiz titled "${quizTitle}" with exactly ${numberOfQuestions} multiple-choice questions. 
Each question must have a question text, an array of 3 to 5 answer options, the 0-indexed integer for the correct answer option, and an optional brief explanation for the correct answer.
Return the response strictly as a JSON object with a single key "questions". The value of "questions" should be an array of question objects. 
Each question object must have the following keys: "questionText" (string), "options" (array of objects, where each object has a "text" string key), "correctAnswerIndex" (number), and "explanation" (string, optional).

Text:
"""
${textContent.substring(0, 18000)} 
"""

Output only the JSON object.`; // Increased substring limit slightly

  const payload: AIPromptPayload = {
    model: model || "google/gemini-2.5-pro-preview", // Default to gemini-2.5-pro-preview as per OpenRouter
    messages: [{ role: "user", content: prompt }],
    // For models that support it, forcing JSON output is highly recommended:
    // response_format: { type: "json_object" },
    // temperature: 0.5, // Lower temperature for more predictable JSON structure
    // max_tokens: 300 * numberOfQuestions, // Estimate tokens needed
  };

  try {
    console.log(
      `Calling AI API: ${baseUrl}/chat/completions with model ${payload.model}`
    );
    const response = await axios.post(`${baseUrl}/chat/completions`, payload, {
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      timeout: 60000, // 60 second timeout for AI generation
    });

    if (
      response.data &&
      response.data.choices &&
      response.data.choices[0] &&
      response.data.choices[0].message
    ) {
      let content = response.data.choices[0].message.content;
      console.log("Raw AI response content:", content);

      // Attempt to clean and parse JSON
      // Remove markdown ```json ... ``` wrappers if present
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/s);
      if (jsonMatch && jsonMatch[1]) {
        content = jsonMatch[1];
      }
      // Remove potential leading/trailing whitespace or non-JSON text before the first '{' and after the last '}'
      const firstBrace = content.indexOf("{");
      const lastBrace = content.lastIndexOf("}");
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        content = content.substring(firstBrace, lastBrace + 1);
      }

      try {
        const parsedJson = JSON.parse(content) as AIResponseFormat;
        if (parsedJson.questions && Array.isArray(parsedJson.questions)) {
          return parsedJson;
        }
        console.error(
          "Parsed JSON does not match expected AIResponseFormat. Parsed:",
          parsedJson
        );
        return null;
      } catch (parseError: any) {
        console.error(
          "Error parsing AI response JSON:",
          parseError.message,
          "Cleaned content was:",
          content
        );
        return null;
      }
    } else {
      console.error(
        "Unexpected AI response structure:",
        JSON.stringify(response.data, null, 2)
      );
      return null;
    }
  } catch (error: any) {
    if (axios.isAxiosError(error)) {
      console.error("Axios error calling AI API:", error.message);
      console.error(
        "AI API Response Error Data:",
        JSON.stringify(error.response?.data, null, 2)
      );
    } else {
      console.error("Generic error calling AI API:", error.message);
    }
    return null;
  }
}

// POST /api/quizzes/generate (if this router is mounted at /api/quizzes)
// Or just /generate if mounted at /api/quiz-express or similar
router.post(
  "/generate",
  async (
    req: Request<{}, GenerateQuizResponse, GenerateQuizParams>,
    res: Response<GenerateQuizResponse>
  ) => {
    const { textContent, documentId, quizTitle, numberOfQuestions, aiConfig } =
      req.body;

    // IMPORTANT: Replace with actual user ID from your auth middleware
    // const userId = (req as any).user?.id; // Example if your middleware adds 'user' to req
    const userId = "00000000-0000-0000-0000-000000000000"; // Placeholder UUID for testing. MUST BE REPLACED.

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated. Placeholder ID is active.",
      });
    }

    if (
      !textContent ||
      !quizTitle ||
      !numberOfQuestions ||
      !aiConfig ||
      !aiConfig.apiKey ||
      !aiConfig.baseUrl ||
      !aiConfig.model
    ) {
      return res.status(400).json({
        success: false,
        message:
          "Missing required parameters: textContent, quizTitle, numberOfQuestions, or complete aiConfig.",
      });
    }
    if (numberOfQuestions <= 0 || numberOfQuestions > 50) {
      // Set reasonable limits
      return res.status(400).json({
        success: false,
        message: "Number of questions must be between 1 and 50.",
      });
    }

    try {
      console.log(
        `Generating quiz: "${quizTitle}" for user ${userId} with ${numberOfQuestions} questions.`
      );
      const aiGeneratedData = await generateQuestionsFromAI(
        textContent,
        numberOfQuestions,
        aiConfig,
        quizTitle
      );

      if (
        !aiGeneratedData ||
        !aiGeneratedData.questions ||
        aiGeneratedData.questions.length === 0
      ) {
        return res.status(500).json({
          success: false,
          message:
            "Failed to generate questions from AI or AI returned no questions in the expected format.",
        });
      }

      let generatedQs = aiGeneratedData.questions;
      if (generatedQs.length > numberOfQuestions) {
        console.warn(
          `AI generated ${generatedQs.length} questions, trimming to requested ${numberOfQuestions}.`
        );
        generatedQs = generatedQs.slice(0, numberOfQuestions);
      }
      // If AI returns fewer, we proceed with what we have. Client should be aware.
      if (generatedQs.length === 0) {
        return res.status(500).json({
          success: false,
          message: "AI generated no usable questions after filtering.",
        });
      }

      // Use a transaction for database operations
      const result = await db.transaction(async (tx) => {
        const newQuizId = uuidv4();
        const quizInsertResult = await tx
          .insert(quizzes)
          .values({
            id: newQuizId,
            userId: userId,
            documentId: documentId || null,
            title: quizTitle,
          })
          .returning();

        if (!quizInsertResult || quizInsertResult.length === 0) {
          throw new Error("Failed to save quiz to database.");
        }
        const savedQuiz = quizInsertResult[0];

        const questionsToInsertData = generatedQs.map((q, index) => ({
          id: uuidv4(),
          quizId: newQuizId,
          userId: userId,
          questionOrder: index + 1,
          questionText: q.questionText,
          options: q.options,
          correctAnswerIndex: q.correctAnswerIndex,
          explanation: q.explanation,
        }));

        if (questionsToInsertData.length > 0) {
          const insertedQuestionsDb = await tx
            .insert(quizQuestions)
            .values(questionsToInsertData)
            .returning();

          if (
            !insertedQuestionsDb ||
            insertedQuestionsDb.length !== questionsToInsertData.length
          ) {
            throw new Error(
              "Failed to save all quiz questions to the database."
            );
          }

          const responseQuestions: SharedQuizQuestion[] =
            insertedQuestionsDb.map((q) => ({
              id: q.id,
              quizId: q.quizId,
              userId: q.userId, // Assuming userId is not null in db schema based on migrations
              questionOrder: q.questionOrder,
              questionText: q.questionText,
              options: q.options as { text: string }[],
              correctAnswerIndex: q.correctAnswerIndex,
              explanation: q.explanation || undefined,
              createdAt: q.createdAt.toISOString(),
              updatedAt: q.updatedAt.toISOString(),
            }));

          const fullQuiz: Quiz = {
            id: savedQuiz.id,
            userId: savedQuiz.userId,
            documentId: savedQuiz.documentId,
            title: savedQuiz.title,
            createdAt: savedQuiz.createdAt.toISOString(),
            updatedAt: savedQuiz.updatedAt.toISOString(),
            questions: responseQuestions,
          };
          return { success: true, quiz: fullQuiz, quizId: newQuizId };
        } else {
          throw new Error("No questions were processed to insert.");
        }
      });

      return res.status(201).json(result);
    } catch (error: any) {
      console.error("Error in /generate quiz endpoint:", error.message);
      console.error(error.stack);
      // If it's an error from our transaction, it might already be a simple message
      if (
        error.message.includes("Failed to save") ||
        error.message.includes("No questions were processed")
      ) {
        res.status(500).json({ success: false, message: error.message });
      } else {
        res.status(500).json({
          success: false,
          message: "An unexpected error occurred during quiz generation.",
        });
      }
    }
  }
);

// GET /:quizId - Fetch a specific quiz and its questions
router.get(
  "/:quizId",
  async (
    req: Request<
      { quizId: string },
      Quiz | { success: boolean; message: string },
      {}
    >,
    res: Response<Quiz | { success: boolean; message: string }>
  ) => {
    const { quizId } = req.params;
    // IMPORTANT: Replace with actual user ID from your auth middleware
    // const userId = (req as any).user?.id;
    const userId = "00000000-0000-0000-0000-000000000000"; // Placeholder UUID. MUST BE REPLACED.

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated. Placeholder ID is active.",
      });
    }

    if (!quizId) {
      return res
        .status(400)
        .json({ success: false, message: "Quiz ID is required." });
    }

    try {
      console.log(`Fetching quiz with ID: ${quizId} for user ${userId}`);

      const quizData = await db.query.quizzes.findFirst({
        where: (quizzes, { eq, and }) =>
          and(eq(quizzes.id, quizId), eq(quizzes.userId, userId)),
        with: {
          questions: {
            orderBy: (questions, { asc }) => [asc(questions.questionOrder)],
          },
        },
      });

      if (!quizData) {
        return res.status(404).json({
          success: false,
          message: "Quiz not found or access denied.",
        });
      }

      // Map to ensure correct structure and types as defined in SharedQuizQuestion
      const responseQuestions: SharedQuizQuestion[] = quizData.questions.map(
        (q) => ({
          id: q.id,
          quizId: q.quizId,
          userId: q.userId,
          questionOrder: q.questionOrder,
          questionText: q.questionText,
          options: q.options as { text: string }[],
          correctAnswerIndex: q.correctAnswerIndex,
          explanation: q.explanation || undefined,
          createdAt: q.createdAt.toISOString(),
          updatedAt: q.updatedAt.toISOString(),
        })
      );

      const fullQuiz: Quiz = {
        id: quizData.id,
        userId: quizData.userId,
        documentId: quizData.documentId,
        title: quizData.title,
        createdAt: quizData.createdAt.toISOString(),
        updatedAt: quizData.updatedAt.toISOString(),
        questions: responseQuestions,
      };

      return res.status(200).json(fullQuiz);
    } catch (error: any) {
      console.error(`Error fetching quiz ${quizId}:`, error.message);
      console.error(error.stack);
      res.status(500).json({
        success: false,
        message: "An unexpected error occurred while fetching the quiz.",
      });
    }
  }
);

export default router;
