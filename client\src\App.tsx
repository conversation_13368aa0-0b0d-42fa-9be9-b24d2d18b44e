import React from "react";
import { Switch, Route } from "wouter";
import Dashboard from "@/pages/Dashboard";
import AIConfig from "@/pages/AIConfig";
import FlashcardReview from "@/pages/FlashcardReview";
import FlashcardsPage from "@/pages/FlashcardsPage";
import FlashcardEditPage from "@/pages/FlashcardEditPage";
import NotFound from "@/pages/not-found";
import AnimatedRoute from "@/components/layout/AnimatedRoute";
import QuizzesPage from "@/pages/QuizzesPage";
import QuizEditPage from "@/pages/QuizEditPage";
import { DocumentViewPage } from "@/pages/DocumentViewPage";
import QuizViewPage from "@/pages/QuizViewPage";

// Helper for ESC key closing modals, might be used by specific components if needed
export const useEscapeKey = (onEscape: () => void) => {
  React.useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onEscape();
      }
    };
    window.addEventListener("keydown", handleEsc);
    return () => window.removeEventListener("keydown", handleEsc);
  }, [onEscape]);
};

function App() {
  return (
    <Switch>
      <Route path="/" component={Dashboard} />{" "}
      {/* Use regular Route for Dashboard to avoid re-animation on internal changes */}
      <AnimatedRoute path="/ai-config" component={AIConfig} />
      <AnimatedRoute path="/flashcards" component={FlashcardsPage} />
      <AnimatedRoute path="/quizzes" component={QuizzesPage} />
      <AnimatedRoute path="/quizzes/:quizId/edit" component={QuizEditPage} />
      <AnimatedRoute path="/quizzes/:quizId" component={QuizViewPage} />
      <AnimatedRoute
        path="/flashcards/edit/:deckId"
        component={FlashcardEditPage}
      />{" "}
      {/* This route is for editing a specific deck */}
      <AnimatedRoute
        path="/flashcards/:deckId"
        component={FlashcardReview}
      />{" "}
      {/* This route is for reviewing a specific deck */}
      <AnimatedRoute
        path="/documents/:documentId"
        component={DocumentViewPage}
      />
      <AnimatedRoute component={NotFound} />
    </Switch>
  );
}

export default App;
