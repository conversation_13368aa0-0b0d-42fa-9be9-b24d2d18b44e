-- Migration to create the user_completions table for tracking completion events

CREATE TABLE IF NOT EXISTS public.user_completions (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  quiz_id UUID NULL REFERENCES public.quizzes (id) ON DELETE CASCADE,
  flashcard_set_id UUID NULL REFERENCES public.flashcard_sets (id) ON DELETE CASCADE,
  completion_type TEXT NOT NULL CHECK (completion_type IN ('quiz', 'flashcard_set')),
  completed_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  score DECIMAL(5,2) NULL, -- Score percentage (0.00-100.00) for quizzes
  time_spent_minutes INTEGER NULL, -- Time spent in minutes
  questions_answered INTEGER NULL, -- For quizzes: total questions answered
  correct_answers INTEGER NULL, -- For quizzes: number of correct answers
  metadata JSONB NULL, -- Additional data (difficulty, settings, etc.)
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  CONSTRAINT user_completions_pkey PRIMARY KEY (id),
  CONSTRAINT completion_resource_check CHECK (
    (completion_type = 'quiz' AND quiz_id IS NOT NULL AND flashcard_set_id IS NULL) OR
    (completion_type = 'flashcard_set' AND flashcard_set_id IS NOT NULL AND quiz_id IS NULL)
  )
);

-- Enable RLS
ALTER TABLE public.user_completions ENABLE ROW LEVEL SECURITY;

-- Policies for user_completions table
-- Users can select their own completions
CREATE POLICY "Users can select their own completions" ON public.user_completions FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own completions
CREATE POLICY "Users can insert their own completions" ON public.user_completions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own completions
CREATE POLICY "Users can update their own completions" ON public.user_completions FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Users can delete their own completions
CREATE POLICY "Users can delete their own completions" ON public.user_completions FOR DELETE
  USING (auth.uid() = user_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_completions_user_id ON public.user_completions (user_id);
CREATE INDEX IF NOT EXISTS idx_user_completions_quiz_id ON public.user_completions (quiz_id);
CREATE INDEX IF NOT EXISTS idx_user_completions_flashcard_set_id ON public.user_completions (flashcard_set_id);
CREATE INDEX IF NOT EXISTS idx_user_completions_type ON public.user_completions (completion_type);
CREATE INDEX IF NOT EXISTS idx_user_completions_completed_at ON public.user_completions (completed_at);
CREATE INDEX IF NOT EXISTS idx_user_completions_user_type ON public.user_completions (user_id, completion_type);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_user_completions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at on row update
CREATE TRIGGER on_user_completions_updated 
  BEFORE UPDATE ON public.user_completions
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_user_completions_updated_at();
