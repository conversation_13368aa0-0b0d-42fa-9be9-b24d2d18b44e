import React, { useState, FormEvent } from "react";
import { supabase } from "../../lib/supabaseClient";
import { useAuth } from "../../hooks/useAuth";
import { Tables, TablesInsert, Enums } from "../../types/supabase";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

type QuizQuestion = Tables<"quiz_questions">;
type QuizQuestionInsert = TablesInsert<"quiz_questions">;
type QuestionType = Enums<"question_type">;

interface McqOption {
  text: string;
  is_correct: boolean;
  id?: string;
}

interface QuestionFormProps {
  selectedQuizId: string;
  editingQuestion?: QuizQuestion | null;
  onQuestionSaved: () => void;
  onCancel: () => void;
}

export const QuestionForm: React.FC<QuestionFormProps> = ({
  selectedQuizId,
  editingQuestion,
  onQuestionSaved,
  onCancel,
}) => {
  const { user } = useAuth();
  const [questionText, setQuestionText] = useState(
    editingQuestion?.question_text || ""
  );
  const [questionType, setQuestionType] = useState<QuestionType>(
    editingQuestion?.type || "multiple_choice"
  );
  const [mcqOptions, setMcqOptions] = useState<McqOption[]>(() => {
    if (
      editingQuestion?.type === "multiple_choice" &&
      editingQuestion.options &&
      Array.isArray(editingQuestion.options)
    ) {
      return (editingQuestion.options as Array<Omit<McqOption, "id">>).map(
        (opt) => ({
          ...opt,
          id: crypto.randomUUID(),
        })
      );
    }
    return [{ text: "", is_correct: false, id: crypto.randomUUID() }];
  });
  const [selectAllOptions, setSelectAllOptions] = useState<McqOption[]>(() => {
    if (
      editingQuestion?.type === "select_all_that_apply" &&
      editingQuestion.options &&
      Array.isArray(editingQuestion.options)
    ) {
      return (editingQuestion.options as Array<Omit<McqOption, "id">>).map(
        (opt) => ({
          ...opt,
          id: crypto.randomUUID(),
        })
      );
    }
    return [{ text: "", is_correct: false, id: crypto.randomUUID() }];
  });
  const [correctAnswerTF, setCorrectAnswerTF] = useState(
    editingQuestion?.correct_answer || "true"
  );
  const [correctAnswerShort, setCorrectAnswerShort] = useState(
    editingQuestion?.correct_answer || ""
  );
  const [explanation, setExplanation] = useState(
    editingQuestion?.explanation || ""
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleMcqOptionChange = (
    index: number,
    field: keyof McqOption,
    value: string | boolean
  ) => {
    const newOptions = [...mcqOptions];
    (newOptions[index] as any)[field] = value;
    if (field === "is_correct" && value === true) {
      newOptions.forEach((opt, i) => {
        if (i !== index) opt.is_correct = false;
      });
    }
    setMcqOptions(newOptions);
  };

  const addMcqOption = () =>
    setMcqOptions([
      ...mcqOptions,
      { text: "", is_correct: false, id: crypto.randomUUID() },
    ]);

  const removeMcqOption = (index: number) =>
    setMcqOptions(mcqOptions.filter((_, i) => i !== index));

  const handleSelectAllOptionChange = (
    index: number,
    field: keyof McqOption,
    value: string | boolean
  ) => {
    const newOptions = [...selectAllOptions];
    (newOptions[index] as any)[field] = value;
    setSelectAllOptions(newOptions);
  };

  const addSelectAllOption = () =>
    setSelectAllOptions([
      ...selectAllOptions,
      { text: "", is_correct: false, id: crypto.randomUUID() },
    ]);

  const removeSelectAllOption = (index: number) =>
    setSelectAllOptions(selectAllOptions.filter((_, i) => i !== index));

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!questionText.trim()) {
      setError("Question text is required.");
      return;
    }
    setLoading(true);
    setError(null);

    let optionsPayload: any = null;
    let correctAnswerPayload: string | null = null;

    if (questionType === "multiple_choice") {
      if (mcqOptions.some((opt) => !opt.text.trim())) {
        setError("All MCQ option texts are required.");
        setLoading(false);
        return;
      }
      if (!mcqOptions.some((opt) => opt.is_correct)) {
        setError("One MCQ option must be marked correct.");
        setLoading(false);
        return;
      }
      optionsPayload = mcqOptions.map(({ id, ...rest }) => rest);
    } else if (questionType === "select_all_that_apply") {
      if (selectAllOptions.some((opt) => !opt.text.trim())) {
        setError("All select-all option texts are required.");
        setLoading(false);
        return;
      }
      if (!selectAllOptions.some((opt) => opt.is_correct)) {
        setError("At least one select-all option must be marked correct.");
        setLoading(false);
        return;
      }
      optionsPayload = selectAllOptions.map(({ id, ...rest }) => rest);
    } else if (questionType === "true_false") {
      correctAnswerPayload = correctAnswerTF;
    } else if (questionType === "short_answer") {
      if (!correctAnswerShort.trim()) {
        setError("Correct answer for short answer is required.");
        setLoading(false);
        return;
      }
      correctAnswerPayload = correctAnswerShort;
    }

    const questionData: Omit<QuizQuestionInsert, "quiz_id" | "user_id"> = {
      question_text: questionText,
      type: questionType,
      options: optionsPayload,
      correct_answer: correctAnswerPayload,
      explanation: explanation || null,
    };

    try {
      if (editingQuestion) {
        const { error: updateError } = await supabase
          .from("quiz_questions")
          .update(questionData)
          .match({ id: editingQuestion.id, user_id: user?.id });
        if (updateError) throw updateError;
      } else {
        const finalQuestionData: QuizQuestionInsert = {
          ...questionData,
          quiz_id: selectedQuizId,
          user_id: user!.id,
        };
        const { error: insertError } = await supabase
          .from("quiz_questions")
          .insert(finalQuestionData);
        if (insertError) throw insertError;
      }
      onQuestionSaved();
    } catch (err: any) {
      setError(err.message || "Failed to save question.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-slate-900 border border-slate-600 rounded-xl p-6 shadow-lg">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center justify-between border-b border-slate-700 pb-4">
          <h5 className="text-lg font-bold text-slate-200 flex items-center">
            {editingQuestion ? (
              <>
                <svg
                  className="w-5 h-5 mr-2 text-yellow-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
                Edit Question
              </>
            ) : (
              <>
                <svg
                  className="w-5 h-5 mr-2 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                    clipRule="evenodd"
                  />
                </svg>
                Add New Question
              </>
            )}
          </h5>
          {editingQuestion && (
            <Button
              type="button"
              variant="ghost"
              onClick={onCancel}
              className="text-slate-400 hover:text-slate-300"
            >
              Cancel
            </Button>
          )}
        </div>

        {error && (
          <div className="bg-red-900/20 border border-red-500/30 text-red-400 text-sm p-3 rounded-lg">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="lg:col-span-2">
            <Label
              htmlFor="questionText"
              className="text-slate-300 font-semibold"
            >
              Question Text*
            </Label>
            <textarea
              id="questionText"
              value={questionText}
              onChange={(e) => setQuestionText(e.target.value)}
              required
              rows={4}
              className="block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors resize-none"
              placeholder="Enter your question here..."
            />
          </div>
          <div>
            <Label
              htmlFor="questionType"
              className="text-slate-300 font-semibold"
            >
              Question Type*
            </Label>
            <select
              id="questionType"
              value={questionType}
              onChange={(e) => setQuestionType(e.target.value as QuestionType)}
              className="block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors"
            >
              <option value="multiple_choice">📝 Multiple Choice</option>
              <option value="select_all_that_apply">
                ☑️ Select All That Apply
              </option>
              <option value="true_false">✓ True/False</option>
              <option value="short_answer">💭 Short Answer</option>
            </select>
          </div>
        </div>

        {questionType === "multiple_choice" && (
          <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600">
            <div className="flex items-center justify-between mb-4">
              <Label className="text-slate-300 font-semibold flex items-center">
                <svg
                  className="w-4 h-4 mr-2 text-blue-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                Multiple Choice Options
              </Label>
              <span className="text-xs text-slate-400 bg-slate-800 px-2 py-1 rounded">
                Select one correct answer
              </span>
            </div>
            <div className="space-y-3">
              {mcqOptions.map((opt, index) => (
                <div
                  key={opt.id}
                  className="flex items-center space-x-3 bg-slate-800/50 p-3 rounded-lg border border-slate-600"
                >
                  <span className="text-slate-400 font-mono text-sm w-6">
                    {String.fromCharCode(65 + index)}.
                  </span>
                  <Input
                    type="text"
                    placeholder={`Enter option ${index + 1}`}
                    value={opt.text}
                    onChange={(e) =>
                      handleMcqOptionChange(index, "text", e.target.value)
                    }
                    required
                    className="flex-grow bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500"
                  />
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={opt.is_correct}
                      onChange={(e) =>
                        handleMcqOptionChange(
                          index,
                          "is_correct",
                          e.target.checked
                        )
                      }
                      className="h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500"
                    />
                    <span className="text-sm text-slate-300">Correct</span>
                  </label>
                  {mcqOptions.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMcqOption(index)}
                      className="text-red-400 hover:text-red-300 p-1"
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={addMcqOption}
                className="border-blue-500/30 text-blue-400"
              >
                Add Option
              </Button>
            </div>
          </div>
        )}

        {questionType === "select_all_that_apply" && (
          <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600">
            <div className="flex items-center justify-between mb-4">
              <Label className="text-slate-300 font-semibold flex items-center">
                <svg
                  className="w-4 h-4 mr-2 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                Select All That Apply Options
              </Label>
              <span className="text-xs text-slate-400 bg-slate-800 px-2 py-1 rounded">
                Multiple correct answers allowed
              </span>
            </div>
            <div className="space-y-3">
              {selectAllOptions.map((opt, index) => (
                <div
                  key={opt.id}
                  className="flex items-center space-x-3 bg-slate-800/50 p-3 rounded-lg border border-slate-600"
                >
                  <span className="text-slate-400 font-mono text-sm w-6">
                    {index + 1}.
                  </span>
                  <Input
                    type="text"
                    placeholder={`Enter option ${index + 1}`}
                    value={opt.text}
                    onChange={(e) =>
                      handleSelectAllOptionChange(index, "text", e.target.value)
                    }
                    required
                    className="flex-grow bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500"
                  />
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={opt.is_correct}
                      onChange={(e) =>
                        handleSelectAllOptionChange(
                          index,
                          "is_correct",
                          e.target.checked
                        )
                      }
                      className="h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500"
                    />
                    <span className="text-sm text-slate-300">Correct</span>
                  </label>
                  {selectAllOptions.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSelectAllOption(index)}
                      className="text-red-400 hover:text-red-300 p-1"
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={addSelectAllOption}
                className="border-blue-500/30 text-blue-400"
              >
                Add Option
              </Button>
            </div>
          </div>
        )}

        {questionType === "true_false" && (
          <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600">
            <Label className="text-slate-300 font-semibold mb-3 flex items-center">
              <svg
                className="w-4 h-4 mr-2 text-yellow-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                  clipRule="evenodd"
                />
              </svg>
              Correct Answer
            </Label>
            <div className="grid grid-cols-2 gap-3">
              {["true", "false"].map((value) => (
                <label
                  key={value}
                  className={`flex items-center justify-center p-3 border-2 rounded-lg cursor-pointer transition-all ${
                    correctAnswerTF === value
                      ? "border-purple-500 bg-purple-900/20 text-purple-300"
                      : "border-slate-600 bg-slate-800/50 text-slate-300 hover:border-slate-500"
                  }`}
                >
                  <input
                    type="radio"
                    name="trueFalse"
                    value={value}
                    checked={correctAnswerTF === value}
                    onChange={(e) => setCorrectAnswerTF(e.target.value)}
                    className="sr-only"
                  />
                  <span className="font-medium capitalize">{value}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {questionType === "short_answer" && (
          <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600">
            <Label
              htmlFor="correctShort"
              className="text-slate-300 font-semibold mb-3 flex items-center"
            >
              <svg
                className="w-4 h-4 mr-2 text-orange-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z"
                  clipRule="evenodd"
                />
              </svg>
              Correct Answer*
            </Label>
            <Input
              id="correctShort"
              value={correctAnswerShort}
              onChange={(e) => setCorrectAnswerShort(e.target.value)}
              required
              placeholder="Enter the expected answer..."
              className="bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500"
            />
          </div>
        )}

        <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600">
          <Label
            htmlFor="explanation"
            className="text-slate-300 font-semibold mb-3 flex items-center"
          >
            <svg
              className="w-4 h-4 mr-2 text-indigo-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                clipRule="evenodd"
              />
            </svg>
            Explanation <span className="text-slate-500">(Optional)</span>
          </Label>
          <textarea
            id="explanation"
            value={explanation}
            onChange={(e) => setExplanation(e.target.value)}
            rows={3}
            placeholder="Provide an explanation for the answer (helps students learn)..."
            className="block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors resize-none"
          />
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-slate-700">
          <Button
            type="submit"
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            {loading ? (
              <>
                <svg
                  className="animate-spin h-4 w-4 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                {editingQuestion ? "Saving..." : "Adding..."}
              </>
            ) : (
              <>
                <svg
                  className="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                {editingQuestion ? "Save Changes" : "Add Question"}
              </>
            )}
          </Button>
          {editingQuestion && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="border-slate-600 text-slate-300 hover:text-slate-100"
            >
              Cancel
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};
