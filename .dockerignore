# Dependencies
node_modules/
.pnp
.pnp.js

# Build outputs
dist/
build/
out/
.next/

# Testing
coverage/

# Environment files
.env*
!.env.example

# Development files
.vscode/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# System files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Temporary files
*.swp
*.swo
*~

# Docker files (to prevent recursive copying)
Dockerfile*
docker-compose*
.dockerignore
