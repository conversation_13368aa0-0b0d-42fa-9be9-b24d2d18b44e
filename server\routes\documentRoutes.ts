import express, { Request, Response } from "express";
import { supabaseClient } from "../middleware/supabaseMiddleware";
import { z } from "zod";

const router = express.Router();

// Test endpoint to verify the router is working
router.get("/test", (req: Request, res: Response) => {
  res.json({
    message: "Document routes are working!",
    timestamp: new Date().toISOString(),
    path: req.path,
  });
});

// Authentication middleware - this assumes you have auth middleware configured
// in the main Express app. If not, you'd need to add token extraction and verification here.

// Helper function to verify the current user is authenticated
// This function returns either an error object or a user object with a properly typed id
async function getAuthenticatedUser(
  req: Request
): Promise<
  | { error: string; details?: string; status: number }
  | { user: { id: string; [key: string]: any } }
> {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { error: "Unauthorized: Missing or malformed token", status: 401 };
    }

    const token = authHeader.split(" ")[1];
    if (!token) {
      return { error: "Unauthorized: Missing token", status: 401 };
    }

    // Using the globally exported Supabase client
    const supabase = supabaseClient;
    const { data, error: getUserError } = await supabase.auth.getUser(token);

    if (getUserError) {
      if (
        getUserError.message.toLowerCase().includes("invalid token") ||
        getUserError.message.includes("jwt")
      ) {
        return {
          error: "Unauthorized: Invalid token",
          details: getUserError.message,
          status: 401,
        };
      }
      return {
        error: "Server error validating token",
        details: getUserError.message,
        status: 500,
      };
    }

    const user = data?.user;
    if (!user) {
      return { error: "Unauthorized: No user found for token", status: 401 };
    }

    // Ensure user.id is defined before returning
    if (!user.id) {
      return { error: "User ID missing from authenticated user", status: 500 };
    }
    return { user: { ...user } }; // user.id is already part of the user object
  } catch (err: any) {
    console.error("Auth error in documentRoutes:", err.message, err.stack);
    return { error: "Authentication error", status: 500 };
  }
}

// Schema for secure document upload through backend
const secureUploadSchema = z.object({
  fileName: z.string().min(1),
  content: z.string().min(1),
  contentType: z.string().min(1),
  sizeBytes: z.number().positive(),
  documentId: z.string().uuid().optional(), // Optional client-generated ID for consistency
});

// POST /api/documents/secure-upload - Secure document upload through backend
router.post("/secure-upload", async (req: Request, res: Response) => {
  // Authenticate the user
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;

  try {
    // Validate the request body
    const parsedBody = secureUploadSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        error: "Invalid upload data",
        details: parsedBody.error.flatten(),
      });
    }

    const { fileName, content, contentType, sizeBytes, documentId } =
      parsedBody.data;

    // Generate document ID if not provided
    const finalDocumentId = documentId || crypto.randomUUID();

    // Create file path for Supabase Storage
    const extractedTextPath = `${user.id}/extracted_text/${finalDocumentId}.txt`;

    try {
      // Upload extracted text content to Supabase Storage
      const textBlob = new Blob([content], { type: "text/plain" });
      const { error: uploadError } = await supabaseClient.storage
        .from("study_materials")
        .upload(extractedTextPath, textBlob, {
          contentType: "text/plain",
          cacheControl: "3600",
          upsert: true, // Allow overwriting if file exists
        });

      if (uploadError) {
        console.error("Storage upload error:", uploadError);
        return res.status(500).json({
          error: "Failed to upload to storage",
          details: uploadError.message,
        });
      }

      // Insert document metadata into Supabase database
      const { data: dbData, error: dbError } = await supabaseClient
        .from("study_documents")
        .insert({
          id: finalDocumentId,
          user_id: user.id,
          file_name: fileName,
          file_path: extractedTextPath,
          content_type: contentType,
          size_bytes: sizeBytes,
          status: "extracted",
          extracted_text_path: extractedTextPath,
          extracted_text_summary:
            content.substring(0, 500) + (content.length > 500 ? "..." : ""),
        })
        .select()
        .single();

      if (dbError) {
        console.error("Database insert error:", dbError);

        // Clean up uploaded file if database insert fails
        await supabaseClient.storage
          .from("study_materials")
          .remove([extractedTextPath]);

        return res.status(500).json({
          error: "Failed to save document metadata",
          details: dbError.message,
        });
      }

      console.log("Document successfully uploaded and saved:", dbData);

      return res.status(201).json({
        success: true,
        document: dbData,
        message: "Document uploaded and processed successfully",
      });
    } catch (storageError: any) {
      console.error("Storage operation error:", storageError);
      return res.status(500).json({
        error: "Storage operation failed",
        details: storageError.message,
      });
    }
  } catch (error: any) {
    console.error("Error in secure upload:", error);
    return res.status(500).json({
      error: "Failed to process document upload",
      details: error.message,
    });
  }
});

// Legacy POST route - use /secure-upload instead
router.post("/", async (req: Request, res: Response) => {
  return res.status(410).json({
    error:
      "This endpoint is deprecated. Use /api/documents/secure-upload instead.",
  });
});

// GET /api/documents - Get all documents for the authenticated user
router.get("/", async (req: Request, res: Response) => {
  // Authenticate the user
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;

  try {
    const { data: userDocuments, error: dbError } = await supabaseClient
      .from("study_documents")
      .select("*")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (dbError) {
      throw new Error(dbError.message);
    }

    return res.status(200).json(userDocuments || []);
  } catch (error: any) {
    console.error("Error fetching documents:", error);
    return res.status(500).json({
      error: "Failed to fetch documents",
      details: error.message,
    });
  }
});

// GET /api/documents/:id/file - Get original document file securely
// IMPORTANT: This route MUST come before /:id to avoid route collision
router.get("/:id/file", async (req: Request, res: Response) => {
  console.log("📄 Document file route hit:", req.params.id);
  console.log("📄 Headers:", req.headers);

  // Authenticate the user
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    console.log("❌ Authentication failed:", authResult.error);
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;
  const documentId = req.params.id;

  if (!documentId) {
    return res.status(400).json({ error: "Document ID is required" });
  }

  try {
    // First, get the document metadata to ensure user owns it
    const { data: document, error: dbError } = await supabaseClient
      .from("study_documents")
      .select("*")
      .eq("id", documentId)
      .eq("user_id", user.id)
      .single();

    if (dbError || !document) {
      return res
        .status(404)
        .json({ error: "Document not found or access denied" });
    }

    // Check if we have an original file path (for PDFs and other binary files)
    const filePath = document.original_file_path || document.file_path;

    if (!filePath) {
      return res.status(404).json({
        error: "Original file not available for this document"
      });
    }

    // Get the original file from storage
    const { data: fileData, error: downloadError } =
      await supabaseClient.storage
        .from("study_materials")
        .download(filePath);

    if (downloadError || !fileData) {
      console.error("Error downloading file:", downloadError);
      return res.status(500).json({
        error: "Failed to retrieve document file",
        details: downloadError?.message,
      });
    }

    // Set appropriate headers for the file type
    res.setHeader("Content-Type", document.content_type || "application/octet-stream");
    res.setHeader(
      "Content-Disposition",
      `inline; filename="${document.file_name}"`
    );

    // Convert blob to buffer and send
    const buffer = Buffer.from(await fileData.arrayBuffer());
    return res.status(200).send(buffer);
  } catch (error: any) {
    console.error("Error serving document file:", error);
    return res.status(500).json({
      error: "Failed to serve document file",
      details: error.message,
    });
  }
});

// GET /api/documents/:id/content - Get document content securely
// IMPORTANT: This route MUST come before /:id to avoid route collision
router.get("/:id/content", async (req: Request, res: Response) => {
  console.log("📄 Document content route hit:", req.params.id);
  console.log("📄 Headers:", req.headers);

  // Authenticate the user
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    console.log("❌ Authentication failed:", authResult.error);
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;
  const documentId = req.params.id;

  if (!documentId) {
    return res.status(400).json({ error: "Document ID is required" });
  }

  try {
    // First, get the document metadata to ensure user owns it
    const { data: document, error: dbError } = await supabaseClient
      .from("study_documents")
      .select("*")
      .eq("id", documentId)
      .eq("user_id", user.id)
      .single();

    if (dbError || !document) {
      return res
        .status(404)
        .json({ error: "Document not found or access denied" });
    }

    // Get the extracted text content from storage
    const { data: fileData, error: downloadError } =
      await supabaseClient.storage
        .from("study_materials")
        .download(document.extracted_text_path || document.file_path);

    if (downloadError || !fileData) {
      console.error("Error downloading file:", downloadError);
      return res.status(500).json({
        error: "Failed to retrieve document content",
        details: downloadError?.message,
      });
    }

    // Convert blob to text
    const textContent = await fileData.text();

    // Set appropriate headers for text content
    res.setHeader("Content-Type", "text/plain; charset=utf-8");
    res.setHeader(
      "Content-Disposition",
      `inline; filename="${document.file_name}.txt"`
    );

    return res.status(200).send(textContent);
  } catch (error: any) {
    console.error("Error serving document content:", error);
    return res.status(500).json({
      error: "Failed to serve document content",
      details: error.message,
    });
  }
});

// GET /api/documents/:id - Get a single document by ID
router.get("/:id", async (req: Request, res: Response) => {
  // Authenticate the user
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;
  const documentId = req.params.id;

  if (!documentId) {
    return res.status(400).json({ error: "Document ID is required" });
  }

  try {
    const { data: document, error: dbError } = await supabaseClient
      .from("study_documents")
      .select("*")
      .eq("id", documentId)
      .eq("user_id", user.id)
      .single();

    if (dbError || !document) {
      return res
        .status(404)
        .json({ error: "Document not found or access denied" });
    }

    return res.status(200).json(document);
  } catch (error: any) {
    console.error("Error fetching document:", error);
    return res.status(500).json({
      error: "Failed to fetch document",
      details: error.message,
    });
  }
});

// PUT /api/documents/:id - Update a document
router.put("/:id", async (req: Request, res: Response) => {
  // Authenticate the user
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;
  const documentId = req.params.id;

  if (!documentId) {
    return res.status(400).json({ error: "Document ID is required" });
  }

  try {
    // First check if document exists and belongs to user
    const { data: existingDocument, error: checkError } = await supabaseClient
      .from("study_documents")
      .select("*")
      .eq("id", documentId)
      .eq("user_id", user.id)
      .single();

    if (checkError || !existingDocument) {
      return res
        .status(404)
        .json({ error: "Document not found or access denied" });
    }

    // Create update schema for Supabase fields
    const updateSchema = z.object({
      file_name: z.string().optional(),
      status: z.string().optional(),
      extracted_text_summary: z.string().optional(),
    });

    const parsedBody = updateSchema.safeParse(req.body);

    if (!parsedBody.success) {
      return res.status(400).json({
        error: "Invalid update data",
        details: parsedBody.error.flatten(),
      });
    }

    const { data: updatedDocument, error: updateError } = await supabaseClient
      .from("study_documents")
      .update({
        ...parsedBody.data,
        updated_at: new Date().toISOString(),
      })
      .eq("id", documentId)
      .eq("user_id", user.id)
      .select()
      .single();

    if (updateError || !updatedDocument) {
      return res.status(500).json({ error: "Failed to update document" });
    }

    return res.status(200).json(updatedDocument);
  } catch (error: any) {
    console.error("Error updating document:", error);
    return res.status(500).json({
      error: "Failed to update document",
      details: error.message,
    });
  }
});

// DELETE /api/documents/:id - Delete a document
router.delete("/:id", async (req: Request, res: Response) => {
  // Authenticate the user
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;
  const documentId = req.params.id;

  if (!documentId) {
    return res.status(400).json({ error: "Document ID is required" });
  }

  try {
    // First check if document exists and belongs to user
    const { data: existingDocument, error: checkError } = await supabaseClient
      .from("study_documents")
      .select("*")
      .eq("id", documentId)
      .eq("user_id", user.id)
      .single();

    if (checkError || !existingDocument) {
      return res
        .status(404)
        .json({ error: "Document not found or access denied" });
    }

    // Delete the file from storage first
    if (existingDocument.file_path) {
      await supabaseClient.storage
        .from("study_materials")
        .remove([existingDocument.file_path]);
    }

    if (
      existingDocument.extracted_text_path &&
      existingDocument.extracted_text_path !== existingDocument.file_path
    ) {
      await supabaseClient.storage
        .from("study_materials")
        .remove([existingDocument.extracted_text_path]);
    }

    // Delete the document record
    const { data: deletedDocument, error: deleteError } = await supabaseClient
      .from("study_documents")
      .delete()
      .eq("id", documentId)
      .eq("user_id", user.id)
      .select()
      .single();

    if (deleteError || !deletedDocument) {
      return res.status(500).json({ error: "Failed to delete document" });
    }

    return res.status(200).json({
      message: "Document deleted successfully",
      document: deletedDocument,
    });
  } catch (error: any) {
    console.error("Error deleting document:", error);
    return res.status(500).json({
      error: "Failed to delete document",
      details: error.message,
    });
  }
});

export default router;
