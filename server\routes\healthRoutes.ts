import express from "express";

const router = express.Router();

/**
 * @route GET /api/health
 * @desc Health check endpoint for monitoring container status
 * @access Public
 */
router.get("/", (req, res) => {
  const healthData = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    version: process.env.npm_package_version || "1.0.0",
  };
  
  res.status(200).json(healthData);
});

/**
 * @route GET /api/health/detailed
 * @desc Detailed health check with memory usage stats
 * @access Public
 */
router.get("/detailed", (req, res) => {
  const memoryUsage = process.memoryUsage();
  
  const healthData = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    version: process.env.npm_package_version || "1.0.0",
    uptime: process.uptime(),
    memory: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`,
    },
  };
  
  res.status(200).json(healthData);
});

export default router;
