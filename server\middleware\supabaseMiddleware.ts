import { Context, Next } from 'hono';
import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from '../config';

// Create Supabase client outside the middleware to avoid creating a new client on each request
const supabaseUrl = supabaseConfig.url;
const supabaseServiceKey = supabaseConfig.serviceRoleKey;

console.log('Using Supabase URL:', supabaseUrl ? '✓ Found' : '✗ Not found');
console.log('Using Supabase Service Key:', supabaseServiceKey ? '✓ Found' : '✗ Not found');

// Create a single Supabase client instance
export const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);

// Middleware to attach the Supabase client to the Hono context
export const supabaseMiddleware = async (c: Context, next: Next) => {
  if (!supabaseClient) {
    console.error('Supabase client initialization failed');
    return c.json({ 
      error: 'Internal Server Configuration Error: Supabase client initialization failed.' 
    }, 500);
  }
  
  // Attach the Supabase client to the context
  c.set('supabase', supabaseClient);
  
  // Log that the middleware is working with request details
  console.log(`Supabase client attached to request context for ${c.req.method} ${c.req.url}`);
  console.log(`Request headers: ${JSON.stringify(Object.fromEntries(c.req.raw.headers.entries()))}`);
  
  await next();
};
