import {
  pgTable,
  text,
  serial,
  integer,
  boolean,
  jsonb,
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Define schema for documents
export const documents = pgTable("documents", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  content: text("content").notNull(),
  type: text("type").notNull(), // pdf, docx, txt, md
  createdAt: integer("created_at").notNull(),
  size: integer("size").notNull(),
  userId: text("user_id").notNull(),
});

export const insertDocumentSchema = createInsertSchema(documents).pick({
  name: true,
  content: true,
  type: true,
  createdAt: true,
  size: true,
  userId: true,
});

// Define schema for flashcard decks
export const flashcardDecks = pgTable("flashcard_decks", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  documentId: integer("document_id").references(() => documents.id),
  createdAt: integer("created_at").notNull(),
  userId: text("user_id").notNull(),
});

export const insertFlashcardDeckSchema = createInsertSchema(
  flashcardDecks
).pick({
  name: true,
  description: true,
  documentId: true,
  createdAt: true,
  userId: true,
});

// Define schema for flashcards
export const flashcards = pgTable("flashcards", {
  id: serial("id").primaryKey(),
  question: text("question").notNull(),
  answer: text("answer").notNull(),
  deckId: integer("deck_id")
    .references(() => flashcardDecks.id)
    .notNull(),
  createdAt: integer("created_at").notNull(),
  lastReviewed: integer("last_reviewed"),
  nextReview: integer("next_review"),
  interval: integer("interval"),
  easinessFactor: integer("easiness_factor"),
  reviewCount: integer("review_count"),
  correct: integer("correct"),
  incorrect: integer("incorrect"),
});

export const insertFlashcardSchema = createInsertSchema(flashcards).pick({
  question: true,
  answer: true,
  deckId: true,
  createdAt: true,
  lastReviewed: true,
  nextReview: true,
  interval: true,
  easinessFactor: true,
  reviewCount: true,
  correct: true,
  incorrect: true,
});

// Define schema for quizzes
export const quizzes = pgTable("quizzes", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  study_document_id: text("study_document_id"),
  created_at: text("created_at").notNull(),
  updated_at: text("updated_at").notNull(),
  user_id: text("user_id").notNull(),
  description: text("description"),
});

export const insertQuizSchema = createInsertSchema(quizzes).pick({
  name: true,
  study_document_id: true,
  created_at: true,
  updated_at: true,
  user_id: true,
  description: true,
});

// Define schema for quiz questions
export const quizQuestions = pgTable("quiz_questions", {
  id: text("id").primaryKey(),
  quiz_id: text("quiz_id")
    .references(() => quizzes.id)
    .notNull(),
  question_text: text("question_text").notNull(),
  type: text("type").notNull(),
  options: jsonb("options").notNull(),
  correct_answer: text("correct_answer"),
  explanation: text("explanation"),
  created_at: text("created_at").notNull(),
  updated_at: text("updated_at").notNull(),
  user_id: text("user_id").notNull(),
  // SRS fields
  srs_level: integer("srs_level").default(0),
  due_at: text("due_at"),
  last_reviewed_at: text("last_reviewed_at"),
  srs_interval: integer("srs_interval"),
  srs_ease_factor: text("srs_ease_factor").default("2.5"),
  srs_repetitions: integer("srs_repetitions").default(0),
  srs_correct_streak: integer("srs_correct_streak").default(0),
});

export const insertQuizQuestionSchema = createInsertSchema(quizQuestions).pick({
  quiz_id: true,
  question_text: true,
  type: true,
  options: true,
  correct_answer: true,
  explanation: true,
  created_at: true,
  updated_at: true,
  user_id: true,
});

// Schema for AI provider settings
export const aiProviderSchema = z.object({
  provider: z.string(),
  baseUrl: z.string().url(),
  apiKey: z.string(),
  model: z.string(),
});

// Define all the types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertDocument = z.infer<typeof insertDocumentSchema>;
export type Document = typeof documents.$inferSelect;

export type InsertFlashcardDeck = z.infer<typeof insertFlashcardDeckSchema>;
export type FlashcardDeck = typeof flashcardDecks.$inferSelect;

export type InsertFlashcard = z.infer<typeof insertFlashcardSchema>;
export type Flashcard = typeof flashcards.$inferSelect;

export type InsertQuiz = z.infer<typeof insertQuizSchema>;
export type Quiz = typeof quizzes.$inferSelect;

export type InsertQuizQuestion = z.infer<typeof insertQuizQuestionSchema>;
export type QuizQuestion = typeof quizQuestions.$inferSelect;

export type AIProvider = z.infer<typeof aiProviderSchema>;
