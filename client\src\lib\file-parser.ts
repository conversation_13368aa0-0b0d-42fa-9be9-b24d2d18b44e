import { extractTextFromPdf } from './pdf-parser';
import { extractTextFromDocx, extractTextFromTxt, extractTextFromMd } from './docx-parser';
import { Document } from '@/types';

/**
 * Extract text from a file based on its type
 * Unified interface for all file parsers
 */
export async function extractTextFromFile(file: File): Promise<Document> {
  const fileType = file.name.toLowerCase();

  if (fileType.endsWith('.pdf')) {
    return extractTextFromPdf(file);
  } else if (fileType.endsWith('.docx')) {
    return extractTextFromDocx(file);
  } else if (fileType.endsWith('.txt')) {
    return extractTextFromTxt(file);
  } else if (fileType.endsWith('.md')) {
    return extractTextFromMd(file);
  } else {
    throw new Error('Unsupported file format. Please upload a PDF, DOCX, TXT, or MD file.');
  }
} 