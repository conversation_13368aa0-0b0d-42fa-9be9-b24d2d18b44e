#!/bin/sh
# Docker production startup script

# Set environment to production
export NODE_ENV=production

# Check if PORT env variable is set, otherwise default to 80
if [ -z "$PORT" ]; then
  echo "PORT environment variable not set, defaulting to 80"
  export PORT=80
fi

# Log startup information
echo "Starting ChewyAI in production mode on port $PORT"
echo "NODE_ENV: $NODE_ENV"

# Start the application
node dist/index.js
