import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import {
  createClient,
  SupabaseClient,
} from "https://esm.sh/@supabase/supabase-js@2";
import { corsHeaders } from "../_shared/cors.ts";

// Define the expected structure of the study_documents table for type safety
// This should ideally match your generated types, but for an Edge Function,
// manual definition or a shared types package is common.
interface StudyDocumentInsert {
  user_id: string;
  file_name: string;
  file_path: string;
  content_type: string;
  size_bytes: number;
  status?: string; // e.g., 'uploaded', 'pending_extraction'
  // storage_object_id might be useful but Supabase storage doesn't directly expose it this way easily
}

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Auth context of the user making the request.
    const supabaseClient: SupabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
      {
        global: {
          headers: { Authorization: req.headers.get("Authorization")! },
        },
      }
    );

    // First, get the user from the session
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      console.error("User retrieval error:", userError);
      return new Response(JSON.stringify({ error: "User not authenticated" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 401,
      });
    }

    const formData = await req.formData();
    const file = formData.get("file") as File | null;

    if (!file) {
      return new Response(JSON.stringify({ error: "File not provided" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      });
    }

    // Allowed MIME types and max size (e.g., 50MB)
    const allowedTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/plain",
      "application/msword",
    ];
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (!allowedTypes.includes(file.type)) {
      return new Response(
        JSON.stringify({
          error: `Invalid file type: ${file.type}. Allowed: ${allowedTypes.join(
            ", "
          )}`,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 415, // Unsupported Media Type
        }
      );
    }

    if (file.size > maxSize) {
      return new Response(
        JSON.stringify({
          error: `File too large. Max size is ${maxSize / (1024 * 1024)}MB.`,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 413, // Payload Too Large
        }
      );
    }

    const fileName = file.name;
    const filePath = `${user.id}/${Date.now()}_${fileName}`.replace(
      /\s+/g,
      "_"
    ); // Sanitize and make unique

    // Upload file to Supabase Storage
    // Use service role client for storage uploads to bypass RLS if policies are restrictive for direct user uploads
    // or if complex logic is needed before upload that RLS can't handle.
    // For this function, user is authenticated, so we can proceed.
    // The RLS policies on storage.objects should allow user to upload to their own folder (user.id/*)
    const { error: uploadError } = await supabaseClient.storage
      .from("study_materials") // Your bucket name
      .upload(filePath, file, {
        contentType: file.type,
        cacheControl: "3600",
        upsert: false, // true to overwrite if file with same path exists
      });

    if (uploadError) {
      console.error("Storage upload error:", uploadError);
      throw new Error(`Storage error: ${uploadError.message}`);
    }

    // Insert metadata into the database
    const documentToInsert: StudyDocumentInsert = {
      user_id: user.id,
      file_name: fileName,
      file_path: filePath,
      content_type: file.type,
      size_bytes: file.size,
      status: "uploaded", // or 'pending_extraction'
    };

    const { data: dbData, error: dbError } = await supabaseClient
      .from("study_documents")
      .insert(documentToInsert)
      .select("id")
      .single(); // Assuming you want the ID of the newly inserted row

    if (dbError) {
      console.error("Database insert error:", dbError);
      // Consider deleting the uploaded file from storage if DB insert fails (rollback)
      await supabaseClient.storage.from("study_materials").remove([filePath]);
      throw new Error(`Database error: ${dbError.message}`);
    }

    return new Response(
      JSON.stringify({
        message: "File uploaded successfully",
        documentId: dbData?.id,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Overall error:", error);
    return new Response(
      JSON.stringify({
        error: error.message || "An unexpected error occurred",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
