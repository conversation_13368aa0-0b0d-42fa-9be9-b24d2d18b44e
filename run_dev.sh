#!/bin/bash
# Development environment startup script for ChewyAI

# Exit on error
set -e

# Echo commands for debugging
set -x

# Check if <PERSON><PERSON> and <PERSON>er Compose are installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Get the absolute path of the script directory (works in bash, sh, and Git Bash on Windows)
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]:-$0}" )" && pwd )"
ENV_FILE="${SCRIPT_DIR}/.env"
ENV_EXAMPLE_FILE="${SCRIPT_DIR}/.env.example"

# Helper function to check if we're running on Windows
is_windows() {
    case "$(uname -s)" in
        CYGWIN*|MINGW*|MSYS*) return 0 ;;
        *) return 1 ;;
    esac
}

# Helper function for proper path handling on Windows
normalize_path() {
    if is_windows; then
        # Convert Windows path to Unix path if needed
        echo "$1" | sed 's/\\/\//g' | sed 's/^\([A-Za-z]\):/\/\1/'
    else
        echo "$1"
    fi
}

# Create .env file from example if it doesn't exist
if [ ! -f "${ENV_FILE}" ]; then
    echo "No .env file found at ${ENV_FILE}, creating from .env.example..."
    if [ -f "${ENV_EXAMPLE_FILE}" ]; then
        cp "${ENV_EXAMPLE_FILE}" "${ENV_FILE}"
        echo "Created .env file from example. Please edit the .env file with your credentials before continuing."
    else
        echo "No .env.example file found at ${ENV_EXAMPLE_FILE}. Creating an empty .env file."
        touch "${ENV_FILE}"
    fi
fi

# Try to load environment variables, but don't fail if we can't
echo "Loading environment variables from ${ENV_FILE}..."
if [ -f "${ENV_FILE}" ]; then
    set -a
    # Use the . command instead of source for better compatibility
    if . "${ENV_FILE}" 2>/dev/null; then
        echo "Successfully loaded environment variables."
    else
        echo "Warning: Could not source .env file, but continuing anyway."
    fi
    set +a
else
    echo "Warning: .env file not found, but continuing without it."
fi

# Development environment variables
export NODE_ENV=development
export PORT=5000
export FRONTEND_URL=http://localhost:3000
export VITE_API_BASE_URL=http://localhost:5000/api

# Build the development containers if needed
echo "Building Docker development environment..."
docker-compose -f docker-compose.dev.yml build

# Create a local .env.docker file for docker-compose to use
DOCKER_ENV_FILE="${SCRIPT_DIR}/.env.docker"
cat > "${DOCKER_ENV_FILE}" << EOL
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000
VITE_API_BASE_URL=http://localhost:5000/api
EOL

# Start the development environment
echo -e "\n========================================="
echo "Starting ChewyAI development environment..."
echo "Frontend will be available at: http://localhost:3000"
echo "Backend API will be available at: http://localhost:5000/api"
echo "Health check endpoint: http://localhost:5000/api/health"
echo -e "=========================================\n"
echo "Press Ctrl+C to stop the containers."

# Start development containers with the environment file
docker-compose -f docker-compose.dev.yml --env-file "${DOCKER_ENV_FILE}" up

# Cleanup after exit
trap "rm -f ${DOCKER_ENV_FILE}" EXIT
