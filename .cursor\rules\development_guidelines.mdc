---
description: 
globs: 
alwaysApply: true
---
# AI Agent Operating Protocol: Autonomous Efficiency, Quality & Readability

## I. Core Directives & Planning

1.  **Autonomous Execution:** Execute tasks comprehensively with minimal intervention. Proactively identify and resolve issues. Optimize for efficient completion without sacrificing quality. Focus on actions directly contributing to the goal.
2.  **Goal Comprehension:** Before starting, fully understand task objectives, constraints, and desired outcomes. **Ask clarifying questions immediately** if ambiguity exists. Do not proceed on assumptions.
3.  **Planning:** For non-trivial tasks, briefly outline the steps, considering potential challenges and solutions.
4.  **Contextual Integration:** Analyze the existing codebase, project structure, patterns, and documentation. Ensure changes integrate seamlessly and consistently.

## II. Code Implementation Standards

5.  **Clean, Concise & Readable Code:**
    * Write clean, readable, maintainable, and efficient code.
    * **Strictly avoid long files or functions.** Decompose complex logic into smaller, focused units.
    * Adhere to project coding standards or idiomatic language practices.
    * Follow general best practices (e.g., DRY, SOLID principles).
    * **Prioritize Object-Oriented Principles (OOP)** where appropriate.
6.  **Atomicity, Modularity & Reusability:**
    * Design components, functions, classes, methods, and utilities to be **atomic (single responsibility)**, modular, and reusable.
    * **Explicitly create reusable units** (classes, methods, components, utils) to maximize code reuse and maintainability.
    * Avoid tight coupling between modules.
7.  **Naming Conventions:**
    * Use clear, descriptive names.
    * **Employ `PascalCase`** for classes, components, interfaces, and types.
    * Use `camelCase` or `snake_case` for variables and functions per language convention.
8.  **Effective Commenting:**
    * Comment code judiciously. Explain the **'why'** of complex or non-obvious logic, not just the 'what'.
    * Document public APIs (purpose, parameters, return values).
    * Keep comments synchronized with code changes.
9.  **Robust Error Handling:** Implement thorough error handling using appropriate mechanisms (e.g., try-catch, result types). Provide informative error messages.
10. **Testing:** Write or update unit/integration tests for new features or bug fixes where applicable and feasible. Verify functionality and aim for reasonable coverage.
11. **Performance Optimization:** Consider performance implications (time/space complexity). Optimize where necessary and justified, balancing gains against readability/maintainability. Avoid premature optimization.

## III. Documentation Standards

12. **Concurrent Documentation:** Update or create relevant documentation (READMEs, code comments, API docs) **concurrently** with code changes. Ensure documentation accurately reflects the current state.
13. **Documentation Scope:** Document new features, significant changes, public APIs, complex logic, architectural decisions, and setup/usage instructions as relevant.
14. **Clarity & Accuracy:** Write clear, concise, and accurate documentation for the intended audience.

## IV. Tool & Rule Utilization

15. **Leverage Tools:** Effectively use all available Cursor tools (code generation, refactoring, linting, debugging, context gathering) to enhance efficiency and quality.
16. **Rule Adherence:** Strictly follow all applicable rules defined in `.cursor/rules`, including this protocol and ChewyAI-specific patterns.
17. **User Autonomy:** Never start servers or development tools - user manages their own environment and startup processes.
18. **Direct Implementation:** Implement solutions immediately without requesting approval, following established patterns and user preferences.
19. **Supabase MCP Preference:** Always use Supabase MCP server for database operations instead of manual SQL or CLI commands.
20. **Security-First Approach:** Follow established security patterns for authentication, file handling, and data access as defined in project rules.

## V. Verification & Completion

18. **Self-Review & Correction:** Before finalizing, thoroughly review code, documentation, and tests. Check for errors, inconsistencies, requirement adherence, and improvement opportunities. Refactor or correct as needed.
19. **Task Fulfillment:** Ensure all aspects of the original request are fully addressed and the primary goal is achieved.
20. **Final Output & Summary:** Provide the final code, documentation, and test changes. Include a concise summary of work completed, key decisions, and any potential follow-up considerations.

