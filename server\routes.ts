import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { body, validationResult } from "express-validator";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import {
  FlashcardDeck,
  GenerateFlashcardsResponse,
  Flashcard as SharedFlashcard, // Renaming to avoid conflict with local Flashcard if any
} from "@shared/types/flashcards";

// Updated Zod schema for flashcard generation request
// Aligns with GenerateFlashcardsRequest from shared types and PRD's ephemeral key handling
const generateFlashcardsApiRequestSchema = z.object({
  textContent: z.string().min(1, "textContent is required"),
  documentId: z.string().min(1, "documentId is required"),
  deckTitle: z.string().optional(),
  count: z.number().int().min(1).max(50).optional().default(10), // Kept count from existing logic
  customPrompt: z.string().optional(), // New: custom prompt for AI generation
  // aiSettings are provided by the client per PRD US002
  // The backend uses these ephemerally
  aiSettings: z.object({
    provider: z.string().min(1, "AI provider is required"), // e.g., "OpenRouter"
    baseUrl: z.string().url("Valid Base URL for AI provider is required"),
    apiKey: z.string().min(1, "AI API Key is required"), // User's API key
    model: z.string().min(1, "AI Model ID is required"), // e.g., "google/gemini-2.5-pro-preview"
  }),
});

// Type for flashcard generation request, inferred from Zod schema
type GenerateFlashcardsApiRequest = z.infer<
  typeof generateFlashcardsApiRequestSchema
>;

export async function registerRoutes(app: Express): Promise<Server> {
  console.log("🔧 Registering main routes...");

  // Health check endpoint - needs /api prefix since it's not mounted under a router
  app.get("/api/health", (req, res) => {
    console.log("💚 Health check endpoint hit");
    res.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      message: "Server is running properly",
    });
  });

  // Debug endpoint to list all registered routes
  app.get("/api/debug/routes", (req, res) => {
    const routes: any[] = [];

    // Helper function to extract routes from Express app
    function extractRoutes(stack: any[], basePath = "") {
      stack.forEach((layer) => {
        if (layer.route) {
          // Regular route
          const methods = Object.keys(layer.route.methods);
          routes.push({
            path: basePath + layer.route.path,
            methods: methods,
            type: "route",
          });
        } else if (layer.name === "router") {
          // Router middleware
          const routerBasePath = layer.regexp.source
            .replace("^\\", "")
            .replace("\\/?(?=\\/|$)", "")
            .replace(/\\\//g, "/");
          extractRoutes(layer.handle.stack, routerBasePath);
        }
      });
    }

    try {
      extractRoutes(app._router?.stack || []);
      res.json({
        message: "Debug route listing",
        routes: routes,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: "Failed to extract routes",
        message: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Environment debug endpoint
  app.get("/api/debug/env", (req, res) => {
    res.json({
      message: "Environment debug info",
      nodeEnv: process.env.NODE_ENV,
      hasViteDbPassword: !!process.env.VITE_DATABASE_PASSWORD,
      timestamp: new Date().toISOString(),
    });
  });

  console.log("✅ Main routes registered successfully");
  const httpServer = createServer(app);
  return httpServer;
}
