---
description: 
globs: 
alwaysApply: true
---
1) Do not stop iterating. keep scaffold implementing and do not stop till entire PRD is implemented

2) Keep referring to the [development_guidelines.mdc](mdc:.cursor/rules/development_guidelines.mdc) and [prd.mdc](mdc:.cursor/rules/prd.mdc)

3) Utilize the memory and rules systems as much as possible to track progress

4) Never try to run my code for me, i will always do it myself in a seperate terminal window