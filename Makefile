.PHONY: dev prod prod-build dev-build down logs clean help

# Default target
help:
	@echo "ChewyAI Docker Commands"
	@echo "======================="
	@echo "make dev        - Start development environment (frontend:3000, backend:5000)"
	@echo "make dev-build  - Build development Docker image"
	@echo "make prod       - Start production environment on port 80"
	@echo "make prod-build - Build production Docker image"
	@echo "make down       - Stop all containers"
	@echo "make logs       - View logs from running containers"
	@echo "make clean      - Remove all Docker containers, volumes and images for this project"

# Development environment
dev-build:
	docker-compose -f docker-compose.dev.yml build

dev:
	docker-compose -f docker-compose.dev.yml up

# Production environment
prod-build:
	docker-compose -f docker-compose.prod.yml build

prod:
	docker-compose -f docker-compose.prod.yml up

# Stop containers
down:
	docker-compose -f docker-compose.dev.yml down
	docker-compose -f docker-compose.prod.yml down

# View logs
logs:
	docker-compose -f docker-compose.prod.yml logs -f

# Clean up
clean:
	docker-compose -f docker-compose.dev.yml down -v
	docker-compose -f docker-compose.prod.yml down -v
	docker rmi $$(docker images -q chewyai_*) 2>/dev/null || true
