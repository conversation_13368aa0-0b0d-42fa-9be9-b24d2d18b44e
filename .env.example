# API Keys and External Services
VITE_OPENROUTER_API_KEY=
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=

# Docker Environment Configuration

# Development Environment
# These are used when running with docker-compose.dev.yml
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000
VITE_API_BASE_URL=http://localhost:5000/api

# Production Environment
# These are used when running with docker-compose.prod.yml
# NODE_ENV=production
# PORT=80