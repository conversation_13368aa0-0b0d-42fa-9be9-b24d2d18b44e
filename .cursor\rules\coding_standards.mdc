---
description: Mandatory Codebase Coding Standards
globs: 
alwaysApply: false
---
# Coding Standards & Style Guide

## Code Style Preferences
- **No comments**: Never add comments to code - keep it clean and self-documenting
- **TypeScript strict mode**: Always use strict TypeScript typing
- **Object-oriented principles**: Follow OOP patterns where appropriate
- **Functional patterns**: Use modern JavaScript/TypeScript functional approaches
- **Consistent naming**: Use PascalCase for classes/components, camelCase for variables/functions

## File Structure Standards
```
// Component files
ComponentName.tsx - React components
componentName.ts - Utility functions/classes
componentName.types.ts - Type definitions

// API files  
api.ts - Main API client functions
apiTypes.ts - API request/response types
apiUtils.ts - Helper functions
```

## Import Organization
```typescript
// External libraries first
import React from "react";
import { useState, useEffect } from "react";

// Internal imports by proximity
import { ComponentName } from "@/components/ui/ComponentName";
import { helperFunction } from "@/lib/utils";
import { TypeName } from "@/types";

// Relative imports last
import "./styles.css";
```

## Error Handling Patterns
```typescript
// Standard try-catch with meaningful errors
try {
  const result = await riskyOperation();
  return result;
} catch (error: any) {
  console.error("Operation failed:", error);
  throw new Error(error.message || "Operation failed");
}

// Conditional error handling
if (!requiredData) {
  throw new Error("Required data is missing");
}
```

## Async/Await Standards
- Always use async/await over Promises.then()
- Handle errors with try-catch blocks
- Use proper TypeScript return types for async functions
- Handle loading states in React components

## React Component Standards
```typescript
interface ComponentProps {
  requiredProp: string;
  optionalProp?: number;
  onAction: (data: DataType) => void;
}

export const ComponentName: React.FC<ComponentProps> = ({ 
  requiredProp, 
  optionalProp, 
  onAction 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleOperation = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiCall();
      onAction(result);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <LoadingComponent />;
  if (error) return <ErrorComponent message={error} />;

  return (
    <div className="component-container">
      <button onClick={handleOperation}>
        {requiredProp}
      </button>
    </div>
  );
};
```

## API Function Standards
```typescript
interface ApiRequest {
  param1: string;
  param2?: number;
}

interface ApiResponse {
  data: DataType;
  message: string;
}

export async function apiFunction(request: ApiRequest): Promise<ApiResponse> {
  const headers = await getAuthHeaders();
  
  const response = await fetch(`${API_BASE_URL}/endpoint`, {
    method: "POST",
    headers,
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }

  return response.json();
}
```

## Type Definition Standards
```typescript
// Use interfaces for object shapes
interface UserData {
  id: string;
  email: string;
  name: string;
  createdAt: Date;
}

// Use types for unions and computed types
type Status = "loading" | "success" | "error";
type UserWithStatus = UserData & { status: Status };

// Use enums for constants
enum DocumentStatus {
  PENDING = "pending",
  EXTRACTED = "extracted", 
  PROCESSED = "processed",
}
```

## State Management Standards
```typescript
// React Query for server state
const { data, isLoading, error } = useQuery({
  queryKey: ["resource", userId],
  queryFn: () => fetchResource(userId),
  enabled: !!userId,
});

// Local state for UI
const [isOpen, setIsOpen] = useState(false);
const [formData, setFormData] = useState<FormData>(initialState);

// Zustand for global client state
interface AppState {
  theme: "light" | "dark";
  setTheme: (theme: "light" | "dark") => void;
}

const useAppStore = create<AppState>((set) => ({
  theme: "dark",
  setTheme: (theme) => set({ theme }),
}));
```

## Backend Route Standards
```typescript
router.post("/endpoint", async (req: Request, res: Response) => {
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res.status(authResult.status).json({
      error: authResult.error,
      details: authResult.details,
    });
  }

  const user = authResult.user;

  try {
    const parsedBody = validationSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        error: "Invalid request data",
        details: parsedBody.error.flatten(),
      });
    }

    const result = await performOperation(parsedBody.data, user.id);
    
    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error("Operation failed:", error);
    return res.status(500).json({
      error: "Operation failed",
      details: error.message,
    });
  }
});
```

## Database Query Standards
```typescript
// Supabase queries with proper typing
const { data, error } = await supabase
  .from("table_name")
  .select("column1, column2, related_table(column)")
  .eq("user_id", userId)
  .order("created_at", { ascending: false })
  .limit(10);

if (error) {
  throw new Error(`Database query failed: ${error.message}`);
}

return data;
```

## Validation Standards
```typescript
// Zod schemas for validation
const requestSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  age: z.number().positive().optional(),
});

type RequestData = z.infer<typeof requestSchema>;
```

## CSS/Styling Standards
```tsx
// Tailwind classes organized by category
<div className={`
  flex items-center justify-between
  p-4 rounded-lg border
  bg-slate-800 border-slate-700
  text-slate-300 hover:bg-slate-700
  transition-colors duration-200
`}>
```

## Performance Patterns
- Lazy load components with React.lazy()
- Memoize expensive computations with useMemo
- Optimize re-renders with useCallback
- Use React Query for data fetching and caching
- Implement proper loading states and error boundaries

