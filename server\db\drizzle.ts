import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { supabaseConfig } from '../config';
import * as schema from '../../shared/schema'; // Assuming your schema is here

// Construct the connection string
// Construct the connection string using the password from config (which should be from .env)
const connectionString = `postgres://postgres:${supabaseConfig.dbPassword}@${supabaseConfig.url.replace('https://', '')}:5432/postgres`;

if (!supabaseConfig.dbPassword || supabaseConfig.dbPassword === 'YOUR_POSTGRES_PASSWORD_PLACEHOLDER_FROM_ENV_OR_DIRECTLY_SET') {
  console.warn('\n**********************************************************************************************************************');
  console.warn('* WARNING: Database password not found or is a placeholder.                                                        *');
  console.warn('* Please ensure VITE_DATABASE_PASSWORD is set correctly in your .env file.                                       *');
  console.warn('**********************************************************************************************************************\n');
}

const client = postgres(connectionString, { prepare: false });
export const db = drizzle(client, { schema });

console.log('drizzle.ts: Database client initialized.');
